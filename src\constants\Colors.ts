export const Colors = {
  // Modern Aesthetic Design System with Dark Green and Olive Green
  background: '#fcf4ec',
  backgroundSecondary: '#f8f9f8',
  backgroundTertiary: '#f2f4f0',
  backgroundQuaternary: '#eaede6',
  foreground: '#09090b',

  // Clean Card System without Blur Effects
  card: '#fcf4ec',
  cardElevated: '#fcf4ec',
  cardForeground: '#09090b',
  cardBorder: '#e0e5d9',
  cardShadow: 'rgba(107, 124, 90, 0.08)',
  cardShadowHover: 'rgba(107, 124, 90, 0.12)',
  cardShadowPressed: 'rgba(107, 124, 90, 0.16)',

  // Solid Colors Instead of Glassmorphism
  solidLight: '#fcf4ec',
  solidMedium: '#f8f9f8',
  solidBorder: '#e0e5d9',
  solidDark: '#f2f4f0',
  solidGreen: '#6B7C5A',
  solidGreenLight: '#8a9a7c',

  // Modern Popover System
  popover: '#fcf4ec',
  popoverForeground: '#09090b',
  popoverShadow: 'rgba(45, 80, 22, 0.1)',

  // Premium Primary System
  primary: '#2d5016',
  primaryForeground: '#fcf4ec',
  primaryHover: '#1a3009',
  primaryPressed: '#0f1a05',

  // Elegant Secondary System
  secondary: '#f5f7f5',
  secondaryForeground: '#4a6741',
  secondaryHover: '#e6ede0',
  secondaryPressed: '#d1dcc8',

  // Refined Muted System
  muted: '#f0f4ed',
  mutedForeground: '#6b7c5a',
  mutedHover: '#e6ede0',

  // Sophisticated Accent System
  accent: '#e6ede0',
  accentForeground: '#2d5016',
  accentHover: '#d1dcc8',

  // Clean Destructive System
  destructive: '#dc2626',
  destructiveForeground: '#fcf4ec',
  destructiveLight: '#fef2f2',

  // Modern Border System
  border: '#e0e5d9',
  borderLight: '#eaede6',
  borderStrong: '#c8d1bc',
  input: '#e0e5d9',
  inputFocus: '#c8d1bc',
  ring: '#6B7C5A',

  // Modern Dark Green and Olive Green Brand System
  brand: '#6B7C5A',           // Primary olive green
  brandSecondary: '#4a5a3d',  // Dark olive green
  brandTertiary: '#8a9a7c',   // Light olive green
  brandQuaternary: '#a5b598', // Very light olive green
  brandForeground: '#fcf4ec',
  brandMuted: '#f2f4f0',      // Ultra light green background
  brandSubtle: '#e8ede4',     // Soft green tint
  brandLight: '#d1dcc8',      // Light green
  brandDark: '#3d4a32',       // Very dark green
  brandDeep: '#2a3322',       // Ultra dark green
  brandAccent: '#7a8c6b',     // Medium olive accent
  brandVibrant: '#5a6b4a',    // Vibrant olive green

  // Premium Brand Gradients (2025 Style)
  brandGradient: 'linear-gradient(135deg, #2d5016 0%, #4a6741 50%, #6b7c5a 100%)',
  brandGradientReverse: 'linear-gradient(135deg, #6b7c5a 0%, #4a6741 50%, #2d5016 100%)',
  brandGradientVertical: 'linear-gradient(180deg, #2d5016 0%, #1a3009 100%)',
  brandGradientSubtle: 'linear-gradient(135deg, #f0f4ed 0%, #e6ede0 100%)',
  brandGradientVibrant: 'linear-gradient(135deg, #3d6b24 0%, #2d5016 50%, #1a3009 100%)',
  brandGradientSoft: 'linear-gradient(135deg, #c8d4bf 0%, #a8b89b 100%)',

  // Advanced Brand Effects & Glows
  brandGlow: 'rgba(45, 80, 22, 0.2)',
  brandGlowStrong: 'rgba(45, 80, 22, 0.3)',
  brandShadow: 'rgba(45, 80, 22, 0.08)',
  brandShadowStrong: 'rgba(45, 80, 22, 0.15)',

  // Premium Background Hierarchy
  backgroundPrimary: '#fcf4ec',
  backgroundAccent: '#f0f4ed',
  backgroundElevated: '#fefffe',

  // Glass Effects
  glass: '#fcf4ec',
  glassBorder: 'rgba(107, 124, 90, 0.2)',
  glassStrong: '#fcf4ec',

  // Ultra-Premium Semantic Colors (2025 Design)
  success: '#16a34a',
  successLight: '#f0fdf4',
  successMuted: '#dcfce7',
  successDark: '#15803d',
  successVibrant: '#22c55e',
  warning: '#ea580c',
  warningLight: '#fcf4ec7ed',
  warningMuted: '#fed7aa',
  warningDark: '#c2410c',
  warningVibrant: '#f97316',
  error: '#dc2626',
  errorLight: '#fef2f2',
  errorMuted: '#fecaca',
  errorDark: '#b91c1c',
  errorVibrant: '#ef4444',
  info: '#2563eb',
  infoLight: '#eff6ff',
  infoMuted: '#dbeafe',
  infoDark: '#1d4ed8',
  infoVibrant: '#3b82f6',

  // Ultra-Advanced Glassmorphism System
  glassUltraLight: '#fcf4ec',
  glassLight: '#fcf4ec',
  glassMedium: '#fcf4ec',
  glassHeavy: '#fcf4ec',
  glassBorderLight: '#fcf4ec',
  glassBorderMedium: '#fcf4ec',
  glassBorderStrong: '#fcf4ec',
  glassDarkLight: 'rgba(0, 0, 0, 0.03)',
  glassDarkMedium: 'rgba(0, 0, 0, 0.05)',
  glassDarkStrong: 'rgba(0, 0, 0, 0.08)',
  glassGreenUltraLight: 'rgba(45, 80, 22, 0.05)',
  glassGreenLight: 'rgba(45, 80, 22, 0.08)',
  glassGreenMedium: 'rgba(45, 80, 22, 0.12)',
  glassGreenStrong: 'rgba(45, 80, 22, 0.18)',

  // Professional Shadow Hierarchy (2025 Standards)
  shadowXs: 'rgba(45, 80, 22, 0.03)',
  shadowSm: 'rgba(45, 80, 22, 0.05)',
  shadow: 'rgba(45, 80, 22, 0.08)',
  shadowMd: 'rgba(45, 80, 22, 0.12)',
  shadowLg: 'rgba(45, 80, 22, 0.16)',
  shadowXl: 'rgba(45, 80, 22, 0.20)',
  shadowXxl: 'rgba(45, 80, 22, 0.25)',
  shadowBrand: 'rgba(45, 80, 22, 0.15)',
  shadowBrandLight: 'rgba(45, 80, 22, 0.08)',
  shadowBrandStrong: 'rgba(45, 80, 22, 0.22)',

  // Advanced Gradient System (2025 Premium)
  gradientOverlay: 'linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.4) 100%)',
  gradientOverlayLight: 'linear-gradient(180deg, rgba(252,244,236,0) 0%, rgba(252,244,236,0.8) 100%)',
  gradientOverlayGreen: 'linear-gradient(180deg, rgba(45,80,22,0) 0%, rgba(45,80,22,0.3) 100%)',
  gradientShimmer: 'linear-gradient(90deg, transparent 0%, rgba(252,244,236,0.4) 50%, transparent 100%)',
  gradientShimmerGreen: 'linear-gradient(90deg, transparent 0%, rgba(45,80,22,0.2) 50%, transparent 100%)',

  // Legacy color aliases for backward compatibility
  text: '#09090b',
  textLight: '#71717a',
  white: '#fcf4ec',
  gray50: '#f9fafb',
  gray100: '#f3f4f6',
  gray200: '#e5e7eb',
  gray300: '#d1d5db',
  gray400: '#9ca3af',
  gray500: '#6b7280',
  gray600: '#4b5563',
  gray700: '#374151',
  gray800: '#1f2937',
  gray900: '#111827',
  primaryOpacity10: 'rgba(24, 24, 27, 0.1)',
};

// 2025 Premium Spacing System (Apple-inspired)
export const Spacing = {
  xxs: 2,
  xs: 4,
  sm: 6,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 20,
  xxxl: 24,
  xxxxl: 32,
  xxxxxl: 40,
  xxxxxxl: 48,
  section: 56,
  screen: 64,
};

// Ultra-Modern Border Radius (2025 Standards)
export const BorderRadius = {
  none: 0,
  xs: 4,
  sm: 6,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 20,
  xxxl: 24,
  xxxxl: 32,
  pill: 50,
  full: 9999,
};

// Premium Animation Timing (60fps optimized)
export const AnimationTiming = {
  instant: 0,
  fast: 150,
  normal: 250,
  slow: 350,
  slower: 500,
  slowest: 750,
  ultra: 1000,
};

// Animation Easing (Apple-inspired)
export const AnimationEasing = {
  linear: 'linear',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
  sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
};

// Premium Blur Effects
export const BlurIntensity = {
  none: 0,
  light: 5,
  medium: 10,
  strong: 15,
  ultra: 20,
  extreme: 30,
};

// Ultra-Premium Shadow System (2025 Design Standards)
export const Shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.16,
    shadowRadius: 24,
    elevation: 12,
  },
  xxl: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.20,
    shadowRadius: 32,
    elevation: 16,
  },
  brand: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 6,
  },
  brandStrong: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.22,
    shadowRadius: 16,
    elevation: 8,
  },
  floating: {
    shadowColor: '#2d5016',
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.25,
    shadowRadius: 40,
    elevation: 20,
  },
};

export const FontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
};

export const FontWeights = {
  light: '300' as const,
  normal: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
  black: '900' as const,
};
