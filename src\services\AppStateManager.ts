import { AppState } from 'react-native';

interface AppStateManagerConfig {
  debounceDelay: number;
  maxRefreshAttempts: number;
  refreshCooldown: number;
}

class AppStateManager {
  private static instance: AppStateManager;
  private currentState: string = AppState.currentState;
  private refreshTimeout: NodeJS.Timeout | null = null;
  private isRefreshing: boolean = false;
  private lastRefreshTime: number = 0;
  private refreshAttempts: number = 0;
  private listeners: Set<(state: string) => void> = new Set();
  private refreshCallbacks: Set<() => Promise<void>> = new Set();

  private config: AppStateManagerConfig = {
    debounceDelay: 500,
    maxRefreshAttempts: 3,
    refreshCooldown: 5000, // 5 seconds between refresh attempts
  };

  static getInstance(): AppStateManager {
    if (!AppStateManager.instance) {
      AppStateManager.instance = new AppStateManager();
    }
    return AppStateManager.instance;
  }

  private constructor() {
    this.initializeAppStateListener();
  }

  private initializeAppStateListener(): void {
    AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }

  private async handleAppStateChange(nextAppState: string): Promise<void> {
    console.log('📱 AppStateManager: State changed from', this.currentState, 'to', nextAppState);

    // Notify all listeners
    this.listeners.forEach(listener => {
      try {
        listener(nextAppState);
      } catch (error) {
        console.error('❌ Error in app state listener:', error);
      }
    });

    // Handle foreground refresh
    if (this.currentState.match(/inactive|background/) && nextAppState === 'active') {
      await this.handleForegroundRefresh();
    }

    this.currentState = nextAppState;
  }

  private async handleForegroundRefresh(): Promise<void> {
    // Clear any pending refresh
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
      this.refreshTimeout = null;
    }

    // Check if we're already refreshing
    if (this.isRefreshing) {
      console.log('⚠️ AppStateManager: Refresh already in progress, skipping...');
      return;
    }

    // Check refresh cooldown
    const now = Date.now();
    if (now - this.lastRefreshTime < this.config.refreshCooldown) {
      console.log('⚠️ AppStateManager: Refresh cooldown active, skipping...');
      return;
    }

    // Check max attempts
    if (this.refreshAttempts >= this.config.maxRefreshAttempts) {
      console.log('⚠️ AppStateManager: Max refresh attempts reached, skipping...');
      return;
    }

    console.log('🔄 AppStateManager: Scheduling foreground refresh...');

    // Debounce refresh to prevent rapid successive calls
    this.refreshTimeout = setTimeout(async () => {
      await this.executeRefresh();
    }, this.config.debounceDelay);
  }

  private async executeRefresh(): Promise<void> {
    if (this.isRefreshing) return;

    this.isRefreshing = true;
    this.refreshAttempts++;
    this.lastRefreshTime = Date.now();

    try {
      console.log(`🔄 AppStateManager: Executing refresh (attempt ${this.refreshAttempts}/${this.config.maxRefreshAttempts})`);

      // Execute all refresh callbacks
      const refreshPromises = Array.from(this.refreshCallbacks).map(async (callback) => {
        try {
          await callback();
        } catch (error) {
          console.error('❌ Error in refresh callback:', error);
        }
      });

      await Promise.all(refreshPromises);

      console.log('✅ AppStateManager: Refresh completed successfully');
      
      // Reset attempts on successful refresh
      this.refreshAttempts = 0;

    } catch (error) {
      console.error('❌ AppStateManager: Refresh failed:', error);
    } finally {
      this.isRefreshing = false;
      this.refreshTimeout = null;
    }
  }

  // Public methods for components to use
  addStateListener(listener: (state: string) => void): () => void {
    this.listeners.add(listener);
    
    // Return cleanup function
    return () => {
      this.listeners.delete(listener);
    };
  }

  addRefreshCallback(callback: () => Promise<void>): () => void {
    this.refreshCallbacks.add(callback);
    
    // Return cleanup function
    return () => {
      this.refreshCallbacks.delete(callback);
    };
  }

  getCurrentState(): string {
    return this.currentState;
  }

  isCurrentlyRefreshing(): boolean {
    return this.isRefreshing;
  }

  // Force refresh (for manual triggers)
  async forceRefresh(): Promise<void> {
    if (this.isRefreshing) {
      console.log('⚠️ AppStateManager: Force refresh requested but already refreshing');
      return;
    }

    console.log('🔄 AppStateManager: Force refresh requested');
    await this.executeRefresh();
  }

  // Reset refresh attempts (useful after successful operations)
  resetRefreshAttempts(): void {
    this.refreshAttempts = 0;
    console.log('🔄 AppStateManager: Refresh attempts reset');
  }

  // Update configuration
  updateConfig(newConfig: Partial<AppStateManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ AppStateManager: Configuration updated:', this.config);
  }

  // Cleanup method
  destroy(): void {
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
    }
    this.listeners.clear();
    this.refreshCallbacks.clear();
    console.log('🧹 AppStateManager: Destroyed');
  }
}

export default AppStateManager.getInstance();
