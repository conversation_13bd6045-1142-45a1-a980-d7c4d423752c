/**
 * NUTRITION ACCURACY TEST
 * 
 * This test verifies that the Plan screen now uses REAL nutrition data
 * from Gemini API instead of hardcoded estimates.
 */

import ApiService from '../services/ApiService';

export class NutritionAccuracyTest {
  
  // Test 1: Verify Recipe API returns proper nutrition data
  static async testRecipeNutritionData() {
    console.log('🧪 Testing Recipe API nutrition data structure...');
    
    try {
      const testMeals = [
        'Grilled Chicken Salad',
        'Quinoa Buddha Bowl',
        'Baked Salmon with Vegetables',
        'Greek Yogurt with Berries',
        'Avocado Toast'
      ];
      
      for (const mealName of testMeals) {
        console.log(`🔍 Testing nutrition data for: ${mealName}`);
        
        const recipe = await ApiService.generateRecipe(mealName);
        
        // Verify recipe structure
        console.log('📊 Recipe structure:', {
          hasTitle: !!recipe.recipeTitle,
          hasCalories: typeof recipe.estimatedCalories === 'number',
          hasMacros: !!recipe.macros,
          macroStructure: {
            protein: recipe.macros?.protein,
            carbs: recipe.macros?.carbs,
            fats: recipe.macros?.fats
          }
        });
        
        // Verify nutrition data is realistic
        const calories = recipe.estimatedCalories;
        const protein = parseFloat(recipe.macros.protein.replace('g', ''));
        const carbs = parseFloat(recipe.macros.carbs.replace('g', ''));
        const fat = parseFloat(recipe.macros.fats.replace('g', ''));
        
        console.log(`✅ ${mealName} nutrition:`, {
          calories,
          protein: `${protein}g`,
          carbs: `${carbs}g`,
          fat: `${fat}g`
        });
        
        // Validate nutrition ranges
        const isValidCalories = calories >= 100 && calories <= 1000;
        const isValidProtein = protein >= 5 && protein <= 100;
        const isValidCarbs = carbs >= 0 && carbs <= 150;
        const isValidFat = fat >= 0 && fat <= 50;
        
        console.log(`🔍 Nutrition validation for ${mealName}:`, {
          calories: isValidCalories ? '✅' : '❌',
          protein: isValidProtein ? '✅' : '❌',
          carbs: isValidCarbs ? '✅' : '❌',
          fat: isValidFat ? '✅' : '❌'
        });
        
        if (!isValidCalories || !isValidProtein || !isValidCarbs || !isValidFat) {
          console.warn(`⚠️ Nutrition data seems unrealistic for ${mealName}`);
        }
      }
      
      console.log('✅ Recipe nutrition data test completed');
      
    } catch (error) {
      console.error('❌ Recipe nutrition data test failed:', error);
      throw error;
    }
  }
  
  // Test 2: Test nutrition data parsing and calculation
  static testNutritionDataParsing() {
    console.log('🧪 Testing nutrition data parsing...');
    
    const testMacros = [
      { protein: '25g', carbs: '30g', fats: '15g' },
      { protein: '35.5g', carbs: '45.2g', fats: '18.7g' },
      { protein: '12g', carbs: '8g', fats: '22g' },
      { protein: '0g', carbs: '25g', fats: '0g' }
    ];
    
    testMacros.forEach((macros, index) => {
      const protein = parseFloat(macros.protein.replace('g', ''));
      const carbs = parseFloat(macros.carbs.replace('g', ''));
      const fat = parseFloat(macros.fats.replace('g', ''));
      
      console.log(`Test ${index + 1}:`, {
        input: macros,
        parsed: { protein, carbs, fat },
        valid: !isNaN(protein) && !isNaN(carbs) && !isNaN(fat)
      });
    });
    
    console.log('✅ Nutrition data parsing test completed');
  }
  
  // Test 3: Test fallback nutrition calculation
  static testFallbackNutritionCalculation() {
    console.log('🧪 Testing fallback nutrition calculation...');
    
    const testCalories = [350, 450, 250, 600, 150];
    
    testCalories.forEach(calories => {
      // Simulate the fallback calculation from Plan screen
      const protein = Math.round(calories * 0.25 / 4); // 25% calories from protein (4 cal/g)
      const carbs = Math.round(calories * 0.45 / 4);   // 45% calories from carbs (4 cal/g)
      const fat = Math.round(calories * 0.30 / 9);     // 30% calories from fat (9 cal/g)
      
      const calculatedCalories = (protein * 4) + (carbs * 4) + (fat * 9);
      const accuracy = Math.abs(calculatedCalories - calories) / calories * 100;
      
      console.log(`${calories} cal fallback:`, {
        protein: `${protein}g`,
        carbs: `${carbs}g`,
        fat: `${fat}g`,
        calculatedCal: calculatedCalories,
        accuracy: `${accuracy.toFixed(1)}% difference`
      });
    });
    
    console.log('✅ Fallback nutrition calculation test completed');
  }
  
  // Test 4: Test meal type calorie estimates
  static testMealTypeCalorieEstimates() {
    console.log('🧪 Testing meal type calorie estimates...');
    
    const mealTypes = [
      { type: 'breakfast', expectedRange: [250, 450] },
      { type: 'lunch', expectedRange: [350, 550] },
      { type: 'dinner', expectedRange: [400, 650] },
      { type: 'snack1', expectedRange: [100, 200] },
      { type: 'snack2', expectedRange: [100, 200] },
      { type: 'snack3', expectedRange: [80, 150] }
    ];
    
    // Simulate getMealInfo function logic
    const getMealCalories = (mealType: string) => {
      const mealInfoMap: { [key: string]: number } = {
        breakfast: 350,
        snack1: 150,
        lunch: 450,
        snack2: 150,
        dinner: 500,
        snack3: 100,
      };
      return mealInfoMap[mealType] || 300;
    };
    
    mealTypes.forEach(({ type, expectedRange }) => {
      const calories = getMealCalories(type);
      const inRange = calories >= expectedRange[0] && calories <= expectedRange[1];
      
      console.log(`${type}:`, {
        calories,
        expectedRange,
        valid: inRange ? '✅' : '❌'
      });
    });
    
    console.log('✅ Meal type calorie estimates test completed');
  }
  
  // Test 5: Integration test - simulate Plan screen workflow
  static async testPlanScreenNutritionWorkflow() {
    console.log('🧪 Testing Plan screen nutrition workflow integration...');
    
    try {
      const testMeal = 'Grilled Chicken Caesar Salad';
      
      console.log(`🔄 Simulating Plan screen workflow for: ${testMeal}`);
      
      // Step 1: Get real nutrition data (like Plan screen does)
      const recipe = await ApiService.generateRecipe(testMeal);
      const realNutrition = {
        calories: recipe.estimatedCalories,
        protein: parseFloat(recipe.macros.protein.replace('g', '')),
        carbs: parseFloat(recipe.macros.carbs.replace('g', '')),
        fat: parseFloat(recipe.macros.fats.replace('g', ''))
      };
      
      console.log('📊 Real nutrition from API:', realNutrition);
      
      // Step 2: Simulate meal logging with real data
      const mealLogData = {
        name: testMeal,
        time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        calories: realNutrition.calories,
        protein: realNutrition.protein,
        carbs: realNutrition.carbs,
        fat: realNutrition.fat,
        type: 'lunch' as const
      };
      
      console.log('📝 Meal log data:', mealLogData);
      
      // Step 3: Validate data quality
      const hasValidNutrition = 
        !isNaN(realNutrition.calories) &&
        !isNaN(realNutrition.protein) &&
        !isNaN(realNutrition.carbs) &&
        !isNaN(realNutrition.fat) &&
        realNutrition.calories > 0;
      
      console.log('✅ Nutrition data validation:', hasValidNutrition ? 'PASSED' : 'FAILED');
      
      if (!hasValidNutrition) {
        throw new Error('Invalid nutrition data received from API');
      }
      
      console.log('✅ Plan screen nutrition workflow test completed successfully');
      
    } catch (error) {
      console.error('❌ Plan screen nutrition workflow test failed:', error);
      throw error;
    }
  }
  
  // Run all nutrition accuracy tests
  static async runAllTests() {
    console.log('🚀 Starting comprehensive nutrition accuracy tests...');
    
    try {
      // Test 1: Recipe API nutrition data
      await this.testRecipeNutritionData();
      
      // Test 2: Nutrition data parsing
      this.testNutritionDataParsing();
      
      // Test 3: Fallback nutrition calculation
      this.testFallbackNutritionCalculation();
      
      // Test 4: Meal type calorie estimates
      this.testMealTypeCalorieEstimates();
      
      // Test 5: Integration test
      await this.testPlanScreenNutritionWorkflow();
      
      console.log('🎉 All nutrition accuracy tests completed successfully!');
      console.log('✅ Plan screen now uses REAL nutrition data from Gemini API');
      console.log('✅ Fallback calculations are mathematically sound');
      console.log('✅ Integration workflow is properly implemented');
      
    } catch (error) {
      console.error('❌ Nutrition accuracy test suite failed:', error);
      throw error;
    }
  }
}

export default NutritionAccuracyTest;
