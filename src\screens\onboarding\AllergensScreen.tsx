import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import MultiSelectCard from '../../components/onboarding/MultiSelectCard';

type AllergensScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'Allergens'>;

const AllergensScreen: React.FC = () => {
  const navigation = useNavigation<AllergensScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [selectedAllergens, setSelectedAllergens] = useState<string[]>(data.allergens);

  const allergenOptions = [
    { id: 'nuts', label: 'Tree Nuts', description: 'Almonds, walnuts, cashews, etc.', icon: 'leaf' as const, color: '#8B5CF6' },
    { id: 'peanuts', label: 'Peanuts', description: 'Peanuts and peanut products', icon: 'ellipse' as const, color: '#F59E0B' },
    { id: 'dairy', label: 'Dairy', description: 'Milk, cheese, yogurt, etc.', icon: 'water' as const, color: '#3B82F6' },
    { id: 'eggs', label: 'Eggs', description: 'Chicken eggs and egg products', icon: 'ellipse-outline' as const, color: '#F97316' },
    { id: 'soy', label: 'Soy', description: 'Soybeans and soy products', icon: 'leaf' as const, color: '#10B981' },
    { id: 'wheat', label: 'Wheat/Gluten', description: 'Wheat, barley, rye', icon: 'flower' as const, color: '#EF4444' },
    { id: 'fish', label: 'Fish', description: 'All types of fish', icon: 'water' as const, color: '#06B6D4' },
    { id: 'shellfish', label: 'Shellfish', description: 'Shrimp, crab, lobster, etc.', icon: 'restaurant' as const, color: '#EC4899' },
    { id: 'sesame', label: 'Sesame', description: 'Sesame seeds and tahini', icon: 'ellipse' as const, color: '#84CC16' },
    { id: 'none', label: 'No Allergies', description: 'I have no food allergies', icon: 'checkmark-circle' as const, color: '#6B7C5A' },
  ];

  const handleNext = () => {
    updateData('allergens', selectedAllergens);
    nextStep();
    navigation.navigate('CuisinePreferences');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  return (
    <OnboardingLayout
      title="Food Allergies"
      subtitle="Help us keep you safe by selecting any food allergies"
      onNext={handleNext}
      onBack={handleBack}
      showSkip={true}
      onSkip={handleNext}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <MultiSelectCard
          title="Select any food allergies you have"
          options={allergenOptions}
          selectedValues={selectedAllergens}
          onSelectionChange={setSelectedAllergens}
          allowMultiple={true}
        />
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    paddingTop: 16,
  },
});

export default AllergensScreen;
