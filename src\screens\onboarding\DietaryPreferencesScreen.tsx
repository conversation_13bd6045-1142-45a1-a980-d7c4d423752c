import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import MultiSelectCard from '../../components/onboarding/MultiSelectCard';

type DietaryPreferencesScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'DietaryPreferences'>;

const DietaryPreferencesScreen: React.FC = () => {
  const navigation = useNavigation<DietaryPreferencesScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [selectedPreferences, setSelectedPreferences] = useState<string[]>(data.dietaryPreferences);

  const dietaryOptions = [
    { id: 'vegetarian', label: 'Vegetarian', description: 'No meat, but includes dairy and eggs', icon: 'leaf' as const, color: '#059669' },
    { id: 'vegan', label: 'Vegan', description: 'No animal products whatsoever', icon: 'flower' as const, color: '#10B981' },
    { id: 'keto', label: 'Ketogenic', description: 'High fat, very low carb diet', icon: 'flame' as const, color: '#F59E0B' },
    { id: 'paleo', label: 'Paleo', description: 'Whole foods, no processed foods', icon: 'fitness' as const, color: '#8B5CF6' },
    { id: 'mediterranean', label: 'Mediterranean', description: 'Fish, olive oil, whole grains', icon: 'water' as const, color: '#3B82F6' },
    { id: 'low_carb', label: 'Low Carb', description: 'Reduced carbohydrate intake', icon: 'trending-down' as const, color: '#EF4444' },
    { id: 'gluten_free', label: 'Gluten Free', description: 'No wheat, barley, or rye', icon: 'close-circle' as const, color: '#F97316' },
    { id: 'dairy_free', label: 'Dairy Free', description: 'No milk or dairy products', icon: 'remove-circle' as const, color: '#84CC16' },
    { id: 'none', label: 'No Restrictions', description: 'I eat everything', icon: 'checkmark-circle' as const, color: '#6B7C5A' },
  ];

  const handleNext = () => {
    updateData('dietaryPreferences', selectedPreferences);
    nextStep();
    navigation.navigate('Allergens');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  return (
    <OnboardingLayout
      title="Dietary Preferences"
      subtitle="Select any dietary preferences you follow"
      onNext={handleNext}
      onBack={handleBack}
      showSkip={true}
      onSkip={handleNext}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <MultiSelectCard
          title="Choose your dietary preferences"
          options={dietaryOptions}
          selectedValues={selectedPreferences}
          onSelectionChange={setSelectedPreferences}
          allowMultiple={true}
        />
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    paddingTop: 16,
  },
});

export default DietaryPreferencesScreen;
