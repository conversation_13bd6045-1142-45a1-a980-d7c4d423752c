#!/usr/bin/env node

/**
 * Setup New Expo Account <PERSON><PERSON><PERSON>
 * 
 * This script helps configure your project for the new Expo account (Saim8888)
 * and creates a new EAS project with proper configuration.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Nutri AI for new Expo account (Saim8888)...\n');

// Step 1: Check if logged into correct account
console.log('1️⃣ Checking Expo account...');
try {
  const whoami = execSync('npx expo whoami', { encoding: 'utf8' }).trim();
  console.log(`Current account: ${whoami}`);
  
  if (whoami !== 'saim8888') {
    console.log('❌ Please login to the correct account first:');
    console.log('   npx expo logout');
    console.log('   npx expo login');
    process.exit(1);
  }
  console.log('✅ Logged into correct account\n');
} catch (error) {
  console.log('❌ Not logged into Expo. Please login first:');
  console.log('   npx expo login');
  process.exit(1);
}

// Step 2: Clean project cache
console.log('2️⃣ Cleaning project cache...');
try {
  // Remove .expo directory if it exists
  if (fs.existsSync('.expo')) {
    execSync('rm -rf .expo', { stdio: 'inherit' });
  }
  
  // Clear Expo cache
  execSync('npx expo start --clear', { stdio: 'pipe' });
  console.log('✅ Project cache cleaned\n');
} catch (error) {
  console.log('⚠️ Cache clean failed, continuing...\n');
}

// Step 3: Initialize new EAS project
console.log('3️⃣ Creating new EAS project...');
try {
  execSync('npx eas project:init', { stdio: 'inherit' });
  console.log('✅ New EAS project created\n');
} catch (error) {
  console.log('⚠️ EAS project init failed, it might already exist\n');
}

// Step 4: Get project info
console.log('4️⃣ Getting project information...');
try {
  const projectInfo = execSync('npx eas project:info', { encoding: 'utf8' });
  console.log('Project Info:');
  console.log(projectInfo);
  
  // Extract project ID from output
  const projectIdMatch = projectInfo.match(/Project ID: ([a-f0-9-]+)/);
  if (projectIdMatch) {
    const newProjectId = projectIdMatch[1];
    console.log(`\n📝 New Project ID: ${newProjectId}`);
    
    // Update app.json with new project ID
    const appJsonPath = path.join(__dirname, 'app.json');
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    appJson.expo.extra.eas.projectId = newProjectId;
    fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
    console.log('✅ Updated app.json with new project ID\n');
  }
} catch (error) {
  console.log('⚠️ Could not get project info, continuing...\n');
}

// Step 5: Configure build profiles
console.log('5️⃣ Configuring build profiles...');
try {
  // Check if eas.json exists and is properly configured
  const easJsonPath = path.join(__dirname, 'eas.json');
  if (fs.existsSync(easJsonPath)) {
    console.log('✅ eas.json already exists and configured\n');
  } else {
    console.log('⚠️ eas.json not found, please run: npx eas build:configure\n');
  }
} catch (error) {
  console.log('⚠️ Build profile configuration failed\n');
}

console.log('🎉 Setup complete! Next steps:');
console.log('');
console.log('1. Verify your configuration:');
console.log('   npx expo whoami');
console.log('   npx eas project:info');
console.log('');
console.log('2. Start a preview build:');
console.log('   npx eas build --profile preview --platform android');
console.log('');
console.log('3. If build fails, try:');
console.log('   npx eas build --profile preview --platform android --clear-cache');
console.log('');
console.log('📱 Your app is now configured for account: saim8888');
console.log('🆔 Bundle ID: com.saim8888.nutriai');
console.log('📦 Package: com.saim8888.nutriai');
