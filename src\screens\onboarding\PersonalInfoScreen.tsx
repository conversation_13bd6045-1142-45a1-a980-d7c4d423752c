import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Animated, {
  FadeInUp,
  SlideInLeft,
} from 'react-native-reanimated';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import { ModernInput, ModernSelect } from '../../components/onboarding/ModernInput';

type PersonalInfoScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'PersonalInfo'>;

const PersonalInfoScreen: React.FC = () => {
  const navigation = useNavigation<PersonalInfoScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [localData, setLocalData] = useState({
    name: data.name,
    age: data.age.toString(),
    gender: data.gender,
    height: data.height.toString(),
    weight: data.weight.toString(),
  });

  const genderOptions = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
  ];

  const updateLocalData = (field: string, value: string) => {
    setLocalData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateData = () => {
    if (!localData.name.trim()) {
      Alert.alert('Missing Information', 'Please enter your name.');
      return false;
    }

    const age = parseInt(localData.age);
    if (!age || age < 13 || age > 120) {
      Alert.alert('Invalid Age', 'Please enter a valid age between 13 and 120.');
      return false;
    }

    if (!localData.gender) {
      Alert.alert('Missing Information', 'Please select your gender.');
      return false;
    }

    const height = parseInt(localData.height);
    if (!height || height < 100 || height > 250) {
      Alert.alert('Invalid Height', 'Please enter a valid height between 100-250 cm.');
      return false;
    }

    const weight = parseFloat(localData.weight);
    if (!weight || weight < 30 || weight > 300) {
      Alert.alert('Invalid Weight', 'Please enter a valid weight between 30-300 kg.');
      return false;
    }

    return true;
  };

  const handleNext = () => {
    if (validateData()) {
      // Update onboarding context with validated data
      updateData('name', localData.name.trim());
      updateData('age', parseInt(localData.age));
      updateData('gender', localData.gender);
      updateData('height', parseInt(localData.height));
      updateData('weight', parseFloat(localData.weight));
      
      nextStep();
      navigation.navigate('HealthGoals');
    }
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  const isFormValid = () => {
    return localData.name.trim() && 
           localData.age && 
           localData.gender && 
           localData.height && 
           localData.weight;
  };

  return (
    <OnboardingLayout
      title="Tell us about yourself"
      subtitle="This helps us personalize your nutrition journey"
      onNext={handleNext}
      onBack={handleBack}
      nextDisabled={!isFormValid()}
    >
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.formContainer}>
          <Animated.View entering={SlideInLeft.delay(300).duration(600)}>
            <ModernInput
              label="Full Name"
              value={localData.name}
              onChangeText={(text) => updateLocalData('name', text)}
              placeholder="Enter your full name"
              icon="person"
              maxLength={50}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(400).duration(600)}>
            <ModernInput
              label="Age"
              value={localData.age}
              onChangeText={(text) => updateLocalData('age', text.replace(/[^0-9]/g, ''))}
              placeholder="Enter your age"
              keyboardType="numeric"
              icon="calendar"
              suffix="years"
              maxLength={3}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(500).duration(600)}>
            <ModernSelect
              label="Gender"
              value={localData.gender}
              onSelect={(value) => updateLocalData('gender', value)}
              options={genderOptions}
              placeholder="Select your gender"
              icon="body"
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(600).duration(600)}>
            <ModernInput
              label="Height"
              value={localData.height}
              onChangeText={(text) => updateLocalData('height', text.replace(/[^0-9]/g, ''))}
              placeholder="Enter your height"
              keyboardType="numeric"
              icon="resize"
              suffix="cm"
              maxLength={3}
            />
          </Animated.View>

          <Animated.View entering={SlideInLeft.delay(700).duration(600)}>
            <ModernInput
              label="Weight"
              value={localData.weight}
              onChangeText={(text) => {
                // Allow decimal numbers
                const cleanText = text.replace(/[^0-9.]/g, '');
                // Prevent multiple decimal points
                const parts = cleanText.split('.');
                if (parts.length > 2) {
                  return;
                }
                updateLocalData('weight', cleanText);
              }}
              placeholder="Enter your weight"
              keyboardType="numeric"
              icon="fitness"
              suffix="kg"
              maxLength={6}
            />
          </Animated.View>
        </Animated.View>
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  formContainer: {
    paddingTop: 16,
  },
});

export default PersonalInfoScreen;
