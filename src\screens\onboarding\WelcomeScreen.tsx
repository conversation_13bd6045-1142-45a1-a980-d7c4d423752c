import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInRight,
  ZoomIn,
  BounceIn,
} from 'react-native-reanimated';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import * as Haptics from 'expo-haptics';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';

import NutritionBackground from '../../components/NutritionBackground';

type WelcomeScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'Welcome'>;

const WelcomeScreen: React.FC = () => {
  const navigation = useNavigation<WelcomeScreenNavigationProp>();
  const { nextStep } = useOnboarding();

  const handleGetStarted = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    nextStep();
    navigation.navigate('PersonalInfo');
  };

  const features = [
    {
      icon: 'scan',
      title: 'AI Food Scanner',
      description: 'Instantly analyze any meal with advanced AI technology',
      color: '#6B7C5A',
    },
    {
      icon: 'restaurant',
      title: 'Smart Recipes',
      description: 'Get personalized recipes based on your preferences',
      color: '#8B9A7A',
    },
    {
      icon: 'fitness',
      title: 'Health Tracking',
      description: 'Monitor your nutrition goals and progress',
      color: '#5A6B4A',
    },
  ];

  return (
    <NutritionBackground variant="onboarding">
      <View style={styles.container}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Hero Section */}
          <Animated.View entering={FadeInDown.delay(200).duration(800)} style={styles.heroSection}>
            <Animated.View entering={ZoomIn.delay(400).duration(600)} style={styles.logoContainer}>
              <Image
                source={require('../../../assets/image final.png')}
                style={styles.logoImage}
                resizeMode="contain"
              />
            </Animated.View>

            <Animated.Text entering={FadeInUp.delay(800).duration(600)} style={styles.tagline}>
              Your Personal Nutrition Assistant
            </Animated.Text>

          <Animated.Text entering={FadeInUp.delay(1000).duration(600)} style={styles.description}>
            Discover the power of AI-driven nutrition tracking. Get personalized meal recommendations,
            scan food instantly, and achieve your health goals with intelligent insights.
          </Animated.Text>
        </Animated.View>

        {/* Features */}
        <Animated.View entering={SlideInLeft.delay(1200).duration(600)} style={styles.featuresSection}>
          {features.map((feature, index) => (
            <Animated.View
              key={feature.title}
              entering={SlideInRight.delay(1400 + index * 200).duration(600)}
              style={styles.featureCard}
            >
              <View style={styles.featureCardContent}>
                <View style={[styles.featureIcon, { backgroundColor: feature.color }]}>
                  <Ionicons name={feature.icon as any} size={24} color="#fcf4ec" />
                </View>
                <View style={styles.featureContent}>
                  <Text style={styles.featureTitle}>{feature.title}</Text>
                  <Text style={styles.featureDescription}>{feature.description}</Text>
                </View>
              </View>
            </Animated.View>
          ))}
        </Animated.View>

        {/* Get Started Button */}
        <Animated.View entering={BounceIn.delay(2000).duration(800)} style={styles.buttonContainer}>
          <TouchableOpacity style={styles.getStartedButton} onPress={handleGetStarted}>
            <View style={styles.getStartedContent}>
              <Text style={styles.getStartedText}>Get Started</Text>
              <Ionicons name="arrow-forward" size={24} color="#fcf4ec" />
            </View>
          </TouchableOpacity>

          <Animated.Text entering={FadeInUp.delay(2200).duration(600)} style={styles.setupTime}>
            Takes less than 2 minutes to set up
          </Animated.Text>
        </Animated.View>
      </ScrollView>
    </View>
    </NutritionBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 40,
  },
  heroSection: {
    alignItems: 'center',
    paddingTop: 40,
    marginBottom: 60,
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  logoImage: {
    width: 200,
    height: 80,
  },
  appName: {
    fontSize: 48,
    fontWeight: '900',
    color: '#6B7C5A',
    marginBottom: 12,
    letterSpacing: -1,
  },
  tagline: {
    fontSize: 20,
    fontWeight: '600',
    color: '#8B9A7A',
    marginBottom: 24,
    letterSpacing: 0.5,
  },
  description: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  featuresSection: {
    gap: 16,
    marginBottom: 60,
  },
  featureCard: {
    borderRadius: 24,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    backgroundColor: '#fcf4ec',
  },
  featureCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fcf4ec',
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  featureDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    lineHeight: 20,
  },
  buttonContainer: {
    alignItems: 'center',
  },
  getStartedButton: {
    borderRadius: 32,
    overflow: 'hidden',
    marginBottom: 16,
    backgroundColor: '#6B7C5A',
  },
  getStartedContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 48,
    gap: 12,
  },
  getStartedText: {
    fontSize: 20,
    fontWeight: '800',
    color: '#fcf4ec',
    letterSpacing: 0.5,
  },
  setupTime: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
  },
});

export default WelcomeScreen;
