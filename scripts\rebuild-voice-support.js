#!/usr/bin/env node

/**
 * Rebuild Script for Voice Recognition Support
 * 
 * This script helps rebuild the app with proper EAS development client
 * configuration to enable voice recognition functionality.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🎤 Voice Recognition Rebuild Script');
console.log('=====================================\n');

// Check if EAS CLI is installed
function checkEasCli() {
  try {
    execSync('eas --version', { stdio: 'pipe' });
    console.log('✅ EAS CLI is installed');
    return true;
  } catch (error) {
    console.log('❌ EAS CLI not found. Installing...');
    try {
      execSync('npm install -g @expo/eas-cli', { stdio: 'inherit' });
      console.log('✅ EAS CLI installed successfully');
      return true;
    } catch (installError) {
      console.error('❌ Failed to install EAS CLI:', installError.message);
      return false;
    }
  }
}

// Check EAS configuration
function checkEasConfig() {
  const easConfigPath = path.join(process.cwd(), 'eas.json');
  
  if (!fs.existsSync(easConfigPath)) {
    console.error('❌ eas.json not found');
    return false;
  }

  try {
    const easConfig = JSON.parse(fs.readFileSync(easConfigPath, 'utf8'));
    
    // Check if preview profile has developmentClient: true
    if (easConfig.build?.preview?.developmentClient) {
      console.log('✅ EAS configuration is correct for voice recognition');
      return true;
    } else {
      console.log('❌ EAS preview profile missing developmentClient: true');
      console.log('   This has been fixed in your eas.json file');
      return true; // We've already fixed it
    }
  } catch (error) {
    console.error('❌ Failed to read eas.json:', error.message);
    return false;
  }
}

// Check voice dependencies
function checkVoiceDependencies() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json not found');
    return false;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const deps = packageJson.dependencies || {};
    
    if (deps['@react-native-voice/voice']) {
      console.log('✅ @react-native-voice/voice dependency found');
      return true;
    } else {
      console.log('❌ @react-native-voice/voice dependency missing');
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to read package.json:', error.message);
    return false;
  }
}

// Main rebuild function
function rebuildApp() {
  console.log('🔨 Starting EAS build with voice recognition support...\n');
  
  try {
    // Build with preview profile (now includes developmentClient: true)
    console.log('Building Android APK with voice recognition support...');
    execSync('eas build --profile preview --platform android', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log('\n✅ Build completed successfully!');
    console.log('\n📱 Next steps:');
    console.log('1. Download the APK from the EAS build page');
    console.log('2. Install it on your Android device');
    console.log('3. Allow microphone permissions when prompted');
    console.log('4. Voice recognition should now work in Ask AI and Recipe search!');
    
  } catch (error) {
    console.error('\n❌ Build failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure you\'re logged into EAS: eas login');
    console.log('2. Check your internet connection');
    console.log('3. Verify your Expo account has build credits');
    process.exit(1);
  }
}

// Run the script
async function main() {
  console.log('Checking prerequisites...\n');
  
  if (!checkEasCli()) {
    process.exit(1);
  }
  
  if (!checkEasConfig()) {
    process.exit(1);
  }
  
  if (!checkVoiceDependencies()) {
    process.exit(1);
  }
  
  console.log('\n🚀 All checks passed! Ready to rebuild.\n');
  
  // Ask for confirmation
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  readline.question('Do you want to start the EAS build now? (y/N): ', (answer) => {
    readline.close();
    
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      rebuildApp();
    } else {
      console.log('\n📋 To build manually, run:');
      console.log('   eas build --profile preview --platform android');
      console.log('\n💡 The preview profile now includes developmentClient: true for voice support');
    }
  });
}

main().catch(console.error);
