#!/usr/bin/env node

/**
 * Fix Gradle Plugin Error <PERSON>t
 * 
 * This script helps resolve the "expo-module-gradle-plugin was not found" error
 * by cleaning caches and ensuring proper configuration.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Expo Module Gradle Plugin Error...\n');

// Step 1: Clean npm cache
console.log('1️⃣ Cleaning npm cache...');
try {
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log('✅ npm cache cleaned\n');
} catch (error) {
  console.log('⚠️ npm cache clean failed, continuing...\n');
}

// Step 2: Remove node_modules and package-lock.json
console.log('2️⃣ Removing node_modules and package-lock.json...');
try {
  if (fs.existsSync('node_modules')) {
    execSync('rm -rf node_modules', { stdio: 'inherit' });
  }
  if (fs.existsSync('package-lock.json')) {
    fs.unlinkSync('package-lock.json');
  }
  console.log('✅ Cleaned project dependencies\n');
} catch (error) {
  console.log('⚠️ Cleanup failed, continuing...\n');
}

// Step 3: Reinstall dependencies
console.log('3️⃣ Reinstalling dependencies...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies reinstalled\n');
} catch (error) {
  console.log('❌ Failed to reinstall dependencies');
  process.exit(1);
}

// Step 4: Clear Expo cache
console.log('4️⃣ Clearing Expo cache...');
try {
  execSync('npx expo install --fix', { stdio: 'inherit' });
  console.log('✅ Expo dependencies fixed\n');
} catch (error) {
  console.log('⚠️ Expo fix failed, continuing...\n');
}

// Step 5: Prebuild clean
console.log('5️⃣ Cleaning prebuild cache...');
try {
  execSync('npx expo prebuild --clean', { stdio: 'inherit' });
  console.log('✅ Prebuild cache cleaned\n');
} catch (error) {
  console.log('⚠️ Prebuild clean failed, this is normal for managed workflow\n');
}

console.log('🎉 Fix complete! Now try building again with:');
console.log('   eas build --profile preview --platform android');
console.log('\n💡 If the error persists, try:');
console.log('   1. Update EAS CLI: npm install -g @expo/eas-cli@latest');
console.log('   2. Clear EAS build cache: eas build --clear-cache');
console.log('   3. Check that your EAS project is properly configured');
