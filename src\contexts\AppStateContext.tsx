import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AppStateContextType {
  isOnboardingComplete: boolean | null;
  completeOnboardingFlow: () => Promise<void>;
  checkOnboardingStatus: () => Promise<void>;
}

const AppStateContext = createContext<AppStateContextType | undefined>(undefined);

export const AppStateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isOnboardingComplete, setIsOnboardingComplete] = useState<boolean | null>(null);

  useEffect(() => {
    checkOnboardingStatus();
  }, []);

  // NOTE: App state handling moved to App.tsx to prevent multiple listeners
  // This prevents refresh loops and ensures centralized state management

  const checkOnboardingStatus = async () => {
    try {
      const onboardingComplete = await AsyncStorage.getItem('onboardingComplete');
      setIsOnboardingComplete(onboardingComplete === 'true');
    } catch (error) {
      setIsOnboardingComplete(false);
    }
  };

  const completeOnboardingFlow = async () => {
    try {
      console.log('🔄 AppStateContext: Starting onboarding flow completion...');

      // Ensure AsyncStorage is updated
      await AsyncStorage.setItem('onboardingComplete', 'true');
      console.log('✅ AppStateContext: onboardingComplete saved to AsyncStorage');

      // Clear any cached navigation state to prevent conflicts
      try {
        await AsyncStorage.removeItem('NAVIGATION_STATE_V1');
        console.log('🧹 AppStateContext: Cleared navigation state cache');
      } catch (navError) {
        console.warn('⚠️ AppStateContext: Could not clear navigation state:', navError);
      }

      // Update state
      setIsOnboardingComplete(true);
      console.log('✅ AppStateContext: isOnboardingComplete state updated to true');

      // Force a re-check to ensure consistency with longer delay
      await new Promise(resolve => setTimeout(resolve, 200));
      await checkOnboardingStatus();
      console.log('✅ AppStateContext: Forced re-check completed');

      // Additional verification
      const finalVerification = await AsyncStorage.getItem('onboardingComplete');
      console.log('🔍 AppStateContext: Final verification:', finalVerification);

    } catch (error) {
      console.error('❌ AppStateContext: Error completing onboarding flow:', error);
      // Still update state even if AsyncStorage fails
      setIsOnboardingComplete(true);
    }
  };

  const contextValue: AppStateContextType = {
    isOnboardingComplete,
    completeOnboardingFlow,
    checkOnboardingStatus,
  };

  return (
    <AppStateContext.Provider value={contextValue}>
      {children}
    </AppStateContext.Provider>
  );
};

export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (context === undefined) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};
