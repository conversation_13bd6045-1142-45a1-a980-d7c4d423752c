# Voice Recognition Debug Guide

## Current Issue
Voice recognition is still not working in EAS preview builds despite having `developmentClient: true` in the configuration.

## Debug Steps

### 1. Check Build Configuration
Verify your `eas.json` has the correct configuration:
```json
{
  "build": {
    "preview": {
      "developmentClient": true,  // ✅ This should be present
      "distribution": "internal",
      "channel": "preview"
    }
  }
}
```

### 2. Verify Voice Library Installation
Check that `@react-native-voice/voice` is properly installed:
```bash
npm list @react-native-voice/voice
```

### 3. Check App Configuration
Verify `app.json` includes the voice plugin:
```json
{
  "plugins": [
    "@react-native-voice/voice"
  ]
}
```

### 4. Debug Voice Service
The VoiceService now includes enhanced debugging. Check the console logs when trying to use voice recognition:

**Expected logs for working voice recognition:**
```
✅ Native voice library loaded successfully
🔍 Voice library methods available: [list of methods]
🔍 Development client detection:
  - __DEV__: true/false
  - NODE_ENV: development/production
  - EAS_BUILD_PROFILE: preview
🔍 Voice.isAvailable() result: true
✅ Native voice recognition initialized successfully
```

**Logs indicating issues:**
```
⚠️ Native voice library not available
❌ Voice recognition not available on this device
❌ Native voice library missing required methods
```

### 5. Manual Testing Steps

1. **Rebuild the app** (this is crucial):
   ```bash
   eas build --profile preview --platform android
   ```

2. **Install the new APK** on your device

3. **Check device permissions**:
   - Go to Settings > Apps > Nutri AI > Permissions
   - Ensure Microphone permission is granted

4. **Test voice recognition**:
   - Open Ask AI screen
   - Tap the microphone button
   - Check console logs for debug information

### 6. Common Issues and Solutions

**Issue**: "Voice recognition requires an EAS development client build"
**Solution**: The app is not detecting it's running in a development client. This could mean:
- The build wasn't created with `developmentClient: true`
- You're using an old APK
- The build failed silently

**Issue**: "Voice recognition not available on this device"
**Solution**: 
- Check microphone permissions
- Ensure device has speech recognition capabilities
- Try on a different device

**Issue**: Voice library loads but fails to initialize
**Solution**:
- Check Android version (requires Android 6.0+)
- Verify Google Speech Services are installed
- Check network connectivity

### 7. Alternative Testing

If voice recognition still doesn't work, test on web browser:
1. Run `expo start --web`
2. Open in Chrome/Safari/Edge
3. Allow microphone permissions
4. Test voice recognition

This will confirm if the issue is with the native implementation or the overall voice service logic.

### 8. Final Troubleshooting

If all else fails:
1. Create a completely new EAS build with development profile:
   ```bash
   eas build --profile development --platform android
   ```
2. Use the development profile instead of preview
3. Start the dev server: `npx expo start --dev-client`
4. Connect to the development build

The development profile is specifically designed for native library testing and should definitely work with voice recognition.
