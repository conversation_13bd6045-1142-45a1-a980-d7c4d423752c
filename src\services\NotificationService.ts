import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import DatabaseIntegrationService from './DatabaseIntegrationService';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface MealReminderNotification {
  id: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  mealName: string;
  scheduledTime: Date;
  isActive: boolean;
}

export interface AchievementNotification {
  id: string;
  title: string;
  message: string;
  achievementType: 'daily_goals' | 'streak' | 'milestone' | 'nutrition';
  data?: any;
}

class NotificationService {
  private static instance: NotificationService;
  private dbService = DatabaseIntegrationService;

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Initialize notification channels without requesting permissions
  async initializeChannels(): Promise<void> {
    try {
      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('meal-reminders', {
          name: 'Meal Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#6B7C5A',
          sound: 'default',
        });

        await Notifications.setNotificationChannelAsync('achievements', {
          name: 'Achievements',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#6B7C5A',
          sound: 'default',
        });
      }

      console.log('✅ Notification channels initialized');
    } catch (error) {
      console.error('❌ Error initializing notification channels:', error);
    }
  }

  // Request notification permissions using latest methods
  async requestPermissions(): Promise<boolean> {
    try {
      console.log('🔍 Requesting notification permissions with latest methods...');

      // For Android 13+ (API 33+), we need to request POST_NOTIFICATIONS permission
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        const { PermissionsAndroid } = require('react-native');

        try {
          const granted = await PermissionsAndroid.request(
            'android.permission.POST_NOTIFICATIONS',
            {
              title: 'Notification Permission',
              message: 'NutriAI needs notification permission to send you meal reminders and health alerts.',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'Allow',
            }
          );

          console.log('📱 Android POST_NOTIFICATIONS permission result:', granted);

          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            console.log('❌ Android notification permission denied');
            return false;
          }
        } catch (androidError) {
          console.log('⚠️ Android notification permission error:', androidError);
          // Continue with Expo method as fallback
        }
      }

      // Use Expo notification permissions (works for all platforms)
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      console.log('📱 Current notification permission status:', existingStatus);

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
            allowAnnouncements: true,
          },
        });
        finalStatus = status;
        console.log('📱 New notification permission status:', finalStatus);
      }

      if (finalStatus !== 'granted') {
        console.log('❌ Notification permission denied');
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('meal-reminders', {
          name: 'Meal Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#6B7C5A',
          sound: 'default',
        });

        await Notifications.setNotificationChannelAsync('achievements', {
          name: 'Achievements',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#6B7C5A',
          sound: 'default',
        });
      }

      console.log('✅ Notification permissions granted');
      return true;
    } catch (error) {
      console.error('❌ Error requesting notification permissions:', error);
      return false;
    }
  }

  // Schedule meal reminder notifications
  async scheduleMealReminders(weeklyPlan: any): Promise<void> {
    try {
      // Cancel existing meal reminders
      await this.cancelMealReminders();

      if (!weeklyPlan || !weeklyPlan.week) {
        console.log('No weekly plan available for meal reminders');
        return;
      }

      const notifications: MealReminderNotification[] = [];

      for (const dayPlan of weeklyPlan.week) {
        const dayDate = this.getDayDate(dayPlan.day);
        
        for (const [mealType, mealName] of Object.entries(dayPlan.meals)) {
          const reminderTime = this.getMealReminderTime(mealType as any, dayDate);
          
          if (reminderTime > new Date()) {
            const notificationId = await Notifications.scheduleNotificationAsync({
              content: {
                title: `🍽️ ${this.getMealTypeDisplayName(mealType)} Reminder`,
                body: `Time for ${mealName}! Don't forget to log your meal.`,
                data: {
                  type: 'meal_reminder',
                  mealType,
                  mealName,
                  day: dayPlan.day,
                },
                sound: 'default',
                priority: Notifications.AndroidNotificationPriority.HIGH,
              },
              trigger: {
                date: reminderTime,
              },
            });

            notifications.push({
              id: notificationId,
              mealType: mealType as any,
              mealName: mealName as string,
              scheduledTime: reminderTime,
              isActive: true,
            });
          }
        }
      }

      console.log(`✅ Scheduled ${notifications.length} meal reminder notifications`);
    } catch (error) {
      console.error('❌ Error scheduling meal reminders:', error);
    }
  }

  // Schedule daily achievement notification
  async scheduleDailyAchievementNotification(): Promise<void> {
    try {
      // Cancel existing achievement notifications
      await this.cancelAchievementNotifications();

      // Schedule for 9 PM every day
      const now = new Date();
      const scheduledTime = new Date();
      scheduledTime.setHours(21, 0, 0, 0); // 9:00 PM

      // If it's already past 9 PM today, schedule for tomorrow
      if (scheduledTime <= now) {
        scheduledTime.setDate(scheduledTime.getDate() + 1);
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🏆 Daily Progress Summary',
          body: 'Check out your achievements for today!',
          data: {
            type: 'daily_achievement',
          },
          sound: 'default',
        },
        trigger: {
          hour: 21,
          minute: 0,
          repeats: true,
        },
      });

      console.log('✅ Scheduled daily achievement notifications');
    } catch (error) {
      console.error('❌ Error scheduling achievement notifications:', error);
    }
  }

  // Send immediate achievement notification
  async sendAchievementNotification(achievement: Omit<AchievementNotification, 'id'>): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: achievement.title,
          body: achievement.message,
          data: {
            type: 'achievement',
            achievementType: achievement.achievementType,
            ...achievement.data,
          },
          sound: 'default',
        },
        trigger: null, // Send immediately
      });

      console.log('✅ Sent achievement notification:', achievement.title);
    } catch (error) {
      console.error('❌ Error sending achievement notification:', error);
    }
  }

  // Generate daily achievement summary
  async generateDailyAchievementSummary(): Promise<void> {
    try {
      const todaysNutrition = await this.dbService.calculateTodaysNutrition();
      const todaysHealth = await this.dbService.loadTodaysHealthData();
      
      const achievements: string[] = [];
      let emoji = '📊';

      // Check nutrition goals
      if (todaysNutrition.calories >= 1800) {
        achievements.push('Met calorie goal');
        emoji = '🎯';
      }
      
      if (todaysNutrition.protein >= 100) {
        achievements.push('Hit protein target');
        emoji = '💪';
      }

      if (todaysNutrition.mealCount >= 3) {
        achievements.push('Logged 3+ meals');
        emoji = '🍽️';
      }

      // Check health goals
      if (todaysHealth?.steps && todaysHealth.steps >= 8000) {
        achievements.push('Reached step goal');
        emoji = '🚶‍♂️';
      }

      if (todaysHealth?.waterIntake && todaysHealth.waterIntake >= 8) {
        achievements.push('Stayed hydrated');
        emoji = '💧';
      }

      let title = `${emoji} Daily Summary`;
      let message = '';

      if (achievements.length > 0) {
        message = `Great job! You ${achievements.join(', ').toLowerCase()}.`;
        title = `🏆 ${achievements.length} Goal${achievements.length > 1 ? 's' : ''} Achieved!`;
      } else {
        message = 'Tomorrow is a new day to reach your health goals!';
        title = '📈 Keep Going!';
      }

      await this.sendAchievementNotification({
        title,
        message,
        achievementType: 'daily_goals',
        data: {
          achievements,
          nutrition: todaysNutrition,
          health: todaysHealth,
        },
      });
    } catch (error) {
      console.error('❌ Error generating daily achievement summary:', error);
    }
  }

  // Cancel meal reminder notifications
  async cancelMealReminders(): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const mealReminderIds = scheduledNotifications
        .filter(notification => notification.content.data?.type === 'meal_reminder')
        .map(notification => notification.identifier);

      for (const id of mealReminderIds) {
        await Notifications.cancelScheduledNotificationAsync(id);
      }

      console.log(`✅ Cancelled ${mealReminderIds.length} meal reminder notifications`);
    } catch (error) {
      console.error('❌ Error cancelling meal reminders:', error);
    }
  }

  // Cancel achievement notifications
  async cancelAchievementNotifications(): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const achievementIds = scheduledNotifications
        .filter(notification => 
          notification.content.data?.type === 'daily_achievement' ||
          notification.content.data?.type === 'achievement'
        )
        .map(notification => notification.identifier);

      for (const id of achievementIds) {
        await Notifications.cancelScheduledNotificationAsync(id);
      }

      console.log(`✅ Cancelled ${achievementIds.length} achievement notifications`);
    } catch (error) {
      console.error('❌ Error cancelling achievement notifications:', error);
    }
  }

  // Cancel all notifications
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('✅ Cancelled all notifications');
    } catch (error) {
      console.error('❌ Error cancelling all notifications:', error);
    }
  }

  // Test notification function
  async sendTestNotification(): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧪 Test Notification',
          body: 'NutriAI notifications are working perfectly!',
          data: {
            type: 'test',
          },
          sound: 'default',
        },
        trigger: {
          seconds: 1,
        },
      });
      console.log('✅ Test notification scheduled');
    } catch (error) {
      console.error('❌ Error sending test notification:', error);
    }
  }

  // Helper methods
  private getDayDate(dayName: string): Date {
    const today = new Date();
    const dayIndex = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].indexOf(dayName);
    const todayIndex = today.getDay();
    
    const daysUntilTarget = (dayIndex - todayIndex + 7) % 7;
    const targetDate = new Date(today);
    targetDate.setDate(today.getDate() + daysUntilTarget);
    
    return targetDate;
  }

  private getMealReminderTime(mealType: string, dayDate: Date): Date {
    const reminderTime = new Date(dayDate);
    
    switch (mealType) {
      case 'breakfast':
        reminderTime.setHours(7, 0, 0, 0); // 7:00 AM
        break;
      case 'lunch':
        reminderTime.setHours(11, 30, 0, 0); // 11:30 AM
        break;
      case 'dinner':
        reminderTime.setHours(18, 0, 0, 0); // 6:00 PM
        break;
      case 'snack':
      case 'snack1':
        reminderTime.setHours(9, 30, 0, 0); // 9:30 AM
        break;
      case 'snack2':
        reminderTime.setHours(14, 30, 0, 0); // 2:30 PM
        break;
      case 'snack3':
        reminderTime.setHours(20, 30, 0, 0); // 8:30 PM
        break;
      default:
        reminderTime.setHours(12, 0, 0, 0); // Default to noon
    }
    
    return reminderTime;
  }

  private getMealTypeDisplayName(mealType: string): string {
    switch (mealType) {
      case 'breakfast': return 'Breakfast';
      case 'lunch': return 'Lunch';
      case 'dinner': return 'Dinner';
      case 'snack':
      case 'snack1': return 'Morning Snack';
      case 'snack2': return 'Afternoon Snack';
      case 'snack3': return 'Evening Snack';
      default: return 'Meal';
    }
  }

  // Get notification permission status
  async getPermissionStatus(): Promise<string> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status;
    } catch (error) {
      console.error('❌ Error getting notification permission status:', error);
      return 'undetermined';
    }
  }

  // Listen for notification responses
  addNotificationResponseListener(callback: (response: Notifications.NotificationResponse) => void) {
    return Notifications.addNotificationResponseReceivedListener(callback);
  }

  // Listen for received notifications
  addNotificationReceivedListener(callback: (notification: Notifications.Notification) => void) {
    return Notifications.addNotificationReceivedListener(callback);
  }
}

export default NotificationService.getInstance();
