/**
 * CROSS-PLATFORM Voice Recognition Service
 *
 * This service provides REAL voice-to-text conversion across platforms:
 *
 * SUPPORTED PLATFORMS:
 * ✅ Web browsers (Chrome, Safari, Edge) - Web Speech API
 * ✅ EAS Development/Production builds - @react-native-voice/voice
 * ✅ iOS native apps - Native speech recognition
 * ✅ Android native apps - Native speech recognition
 * ❌ Expo Go (not supported - will show helpful error)
 *
 * USAGE:
 * - Web: Automatic Web Speech API
 * - Native: Uses @react-native-voice/voice library
 * - EAS Build: Full native speech recognition support
 */
import { Platform } from 'react-native';

// Conditional import for expo-constants with fallback
let Constants: any = null;
try {
  Constants = require('expo-constants').default;
} catch (error) {
  console.log('⚠️ expo-constants not available, using fallback detection');
}

// Conditional import for native voice recognition with enhanced error handling
let Voice: any = null;
let isVoiceLibraryAvailable = false;

// Check if we're in an EAS build environment with fallback detection
const isEASBuild = Constants ? (
  Constants.executionEnvironment === 'standalone' ||
  Constants.executionEnvironment === 'storeClient' ||
  Constants.appOwnership === 'standalone' ||
  __DEV__ === false
) : (
  // Fallback detection for when Constants is not available
  __DEV__ === false ||
  typeof __DEV__ === 'undefined' ||
  process.env.NODE_ENV === 'production'
);

// Check if we're running in a development client build
function isDevelopmentClient(): boolean {
  try {
    // Check for development client indicators
    const isDev = (global as any).__DEV__ || process.env.NODE_ENV === 'development';
    const hasDevClient = (global as any).__DEV__ || (global as any).expo?.modules?.ExpoDevClient;

    // Check for EAS development client environment
    const isEASDevClient = (global as any).expo?.modules?.ExpoDevClient ||
                          (global as any).__expo?.modules?.ExpoDevClient ||
                          process.env.EAS_BUILD_PROFILE === 'development' ||
                          process.env.EAS_BUILD_PROFILE === 'preview';

    console.log('🔍 Development client detection:');
    console.log('  - __DEV__:', (global as any).__DEV__);
    console.log('  - NODE_ENV:', process.env.NODE_ENV);
    console.log('  - EAS_BUILD_PROFILE:', process.env.EAS_BUILD_PROFILE);
    console.log('  - Has dev client modules:', hasDevClient);
    console.log('  - Is EAS dev client:', isEASDevClient);

    return isDev || hasDevClient || isEASDevClient;
  } catch (error) {
    console.log('⚠️ Error detecting development client:', error);
    return false;
  }
}

try {
  // Only try to import if we're in a native environment (not web and not Expo Go)
  if (Platform.OS !== 'web' && isEASBuild) {
    console.log('🎤 Attempting to import @react-native-voice/voice in EAS build...');

    // Try direct import first
    Voice = require('@react-native-voice/voice');

    console.log('🔍 Raw Voice import:', Voice);
    console.log('🔍 Voice type:', typeof Voice);
    console.log('🔍 Voice keys:', Voice ? Object.keys(Voice) : 'Voice is null/undefined');
    console.log('🔍 EAS Build detected:', isEASBuild);
    console.log('🔍 Platform:', Platform.OS);

    // Verify the Voice object has required methods
    if (Voice && typeof Voice.isAvailable === 'function') {
      isVoiceLibraryAvailable = true;
      console.log('✅ Native voice library loaded successfully');
      console.log('🔍 Voice library methods available:', Object.keys(Voice).filter(key => typeof Voice[key] === 'function'));
    } else {
      console.log('⚠️ Native voice library missing required methods');
      console.log('🔍 Voice object:', Voice);
      console.log('🔍 Voice.isAvailable type:', typeof Voice?.isAvailable);

      // Try alternative import methods for different build configurations
      try {
        console.log('🔄 Trying alternative import: Voice.default');
        const VoiceDefault = Voice?.default;
        if (VoiceDefault && typeof VoiceDefault.isAvailable === 'function') {
          Voice = VoiceDefault;
          isVoiceLibraryAvailable = true;
          console.log('✅ Alternative import successful');
          console.log('🔍 Voice.default methods:', Object.keys(VoiceDefault).filter(key => typeof VoiceDefault[key] === 'function'));
        } else {
          // Try named import for EAS builds
          console.log('🔄 Trying named import for EAS build...');
          try {
            const { default: VoiceNamed } = require('@react-native-voice/voice');
            if (VoiceNamed && typeof VoiceNamed.isAvailable === 'function') {
              Voice = VoiceNamed;
              isVoiceLibraryAvailable = true;
              console.log('✅ Named import successful');
              console.log('🔍 Named import methods:', Object.keys(VoiceNamed).filter(key => typeof VoiceNamed[key] === 'function'));
            } else {
              console.log('❌ All import methods failed');
              Voice = null;
            }
          } catch (namedError) {
            console.log('❌ Named import failed:', namedError);
            Voice = null;
          }
        }
      } catch (altError) {
        console.log('❌ Alternative import error:', altError);
        Voice = null;
      }
    }
  } else if (Platform.OS !== 'web') {
    console.log('⚠️ Not in EAS build environment, voice recognition may not work');
    console.log('💡 Voice recognition requires EAS development client build');
    console.log('🔧 Run: eas build --profile preview --platform android');
  } else {
    console.log('📱 Web platform detected, will use Web Speech API');
  }
} catch (error) {
  console.log('⚠️ Native voice library not available, using web fallback:', error);
  console.log('🔍 Error details:', error instanceof Error ? error.message : String(error));
  Voice = null;
  isVoiceLibraryAvailable = false;
}

export interface VoiceRecognitionResult {
  text: string;
  confidence: number;
  isFinal: boolean;
}

export interface VoiceServiceCallbacks {
  onStart?: () => void;
  onResult?: (result: VoiceRecognitionResult) => void;
  onEnd?: () => void;
  onError?: (error: string) => void;
  onPartialResult?: (text: string) => void;
}

class VoiceService {
  private static isListening = false;
  private static callbacks: VoiceServiceCallbacks = {};
  private static currentRecognition: any = null;
  private static isNativeVoiceInitialized = false;

  static async initialize(): Promise<boolean> {
    try {
      console.log('🎤 Initializing Voice Service...');
      console.log('🎤 Platform:', Platform.OS);
      console.log('🎤 Voice library available:', isVoiceLibraryAvailable);
      console.log('🎤 Voice object exists:', !!Voice);
      console.log('🎤 Voice object type:', typeof Voice);
      console.log('🎤 EAS Build:', isEASBuild);

      // Enhanced debugging for Voice object
      if (Voice) {
        console.log('🔍 Voice object keys:', Object.keys(Voice));
        console.log('🔍 Voice.isAvailable type:', typeof Voice.isAvailable);
        console.log('🔍 Voice methods:', Object.keys(Voice).filter(key => typeof Voice[key] === 'function'));
      }

      // Check for native voice recognition first (EAS builds) with enhanced safety
      if (Voice && Platform.OS !== 'web' && isVoiceLibraryAvailable && isEASBuild) {
        console.log('✅ Native voice recognition available in EAS build - initializing...');
        try {
          const initialized = await this.initializeNativeVoice();
          console.log('🔍 initializeNativeVoice result:', initialized);
          if (initialized) {
            console.log('✅ Native voice recognition initialized successfully');
            return true;
          } else {
            console.log('❌ initializeNativeVoice returned false');
          }
        } catch (nativeError) {
          console.error('❌ Native voice initialization failed:', nativeError);
          // Fall through to web speech API check
        }
      } else {
        console.log('⚠️ Native voice not available.');
        console.log('🔍 Debug info:');
        console.log('   - Voice object:', !!Voice);
        console.log('   - Platform:', Platform.OS);
        console.log('   - Library available:', isVoiceLibraryAvailable);
        console.log('   - EAS Build:', isEASBuild);

        if (!isEASBuild && Platform.OS !== 'web') {
          console.log('💡 Voice recognition requires EAS development client build');
          console.log('🔧 Run: eas build --profile preview --platform android');
        }
      }

      // Check for Web Speech API support (web browsers)
      if (this.isWebSpeechAvailable()) {
        console.log('✅ Web Speech API detected and ready');
        return true;
      }

      // No voice recognition available
      console.log('ℹ️ Voice recognition not available on this platform');
      console.log('💡 Voice recognition works best with:');
      console.log('   1. Web: Chrome, Safari, or Edge browser');
      console.log('   2. Mobile: Production builds with native support');
      console.log('   3. You can still use text input for all features!');

      return false;
    } catch (error) {
      console.error('Error initializing voice service:', error);
      return false;
    }
  }

  private static async initializeNativeVoice(): Promise<boolean> {
    try {
      console.log('🔍 initializeNativeVoice starting...');
      console.log('🔍 Voice object:', !!Voice);
      console.log('🔍 isVoiceLibraryAvailable:', isVoiceLibraryAvailable);

      if (!Voice || !isVoiceLibraryAvailable) {
        console.error('❌ Native voice library not available or not properly loaded');
        console.error('   - Voice:', !!Voice);
        console.error('   - isVoiceLibraryAvailable:', isVoiceLibraryAvailable);
        return false;
      }

      // First check if voice recognition is available on the device
      try {
        console.log('🔍 Calling Voice.isAvailable()...');
        const isAvailable = await Voice.isAvailable();
        console.log('🔍 Voice.isAvailable() result:', isAvailable);
        if (!isAvailable) {
          console.error('❌ Voice recognition not available on this device');
          return false;
        }
      } catch (availabilityError) {
        console.error('❌ Error checking voice availability:', availabilityError);
        console.error('❌ Error details:', availabilityError instanceof Error ? availabilityError.message : String(availabilityError));
        return false;
      }

      // Verify Voice object has required CORE methods (not event handlers)
      const requiredMethods = ['isAvailable', 'start', 'stop', 'destroy'];
      for (const method of requiredMethods) {
        if (typeof Voice[method] !== 'function') {
          console.error(`❌ Native voice library missing required method: ${method}`);
          console.error(`🔍 Voice.${method} type:`, typeof Voice[method]);
          return false;
        }
      }
      console.log('✅ All required Voice methods are available');

      // Set up native voice event listeners with error handling
      try {
        Voice.onSpeechStart = () => {
          console.log('🎤 ✅ Native voice recognition started');
          this.isListening = true;
          this.callbacks.onStart?.();
        };
      } catch (error) {
        console.error('❌ Error setting up onSpeechStart listener:', error);
        return false;
      }

      Voice.onSpeechRecognized = () => {
        console.log('🎤 📝 Native voice recognized');
      };

      Voice.onSpeechPartialResults = (event: any) => {
        const partialText = event.value?.[0] || '';
        console.log('🎤 Partial result:', partialText);
        this.callbacks.onPartialResult?.(partialText);
      };

      Voice.onSpeechResults = (event: any) => {
        const results = event.value || [];
        if (results.length > 0) {
          const text = results[0];
          console.log('✅ Native voice result:', text);
          this.callbacks.onResult?.({
            text,
            confidence: 0.9, // Native doesn't provide confidence
            isFinal: true
          });
        }
        this.isListening = false;
      };

      Voice.onSpeechError = (event: any) => {
        console.error('🎤 ❌ Native voice error:', event.error);
        this.isListening = false;

        let errorMessage = 'Voice recognition failed';
        if (event.error?.message) {
          errorMessage = event.error.message;
        }

        this.callbacks.onError?.(errorMessage);
      };

      Voice.onSpeechEnd = () => {
        console.log('🎤 Native voice recognition ended');
        this.isListening = false;
        this.callbacks.onEnd?.();
      };

      this.isNativeVoiceInitialized = true;
      console.log('✅ Native voice recognition initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize native voice:', error);
      return false;
    }
  }

  static async startListening(callbacks: VoiceServiceCallbacks = {}): Promise<boolean> {
    try {
      if (this.isListening) {
        await this.stopListening();
      }

      this.callbacks = callbacks;

      // Try native voice recognition first (EAS builds) with enhanced safety
      if (Voice && Platform.OS !== 'web' && this.isNativeVoiceInitialized && isVoiceLibraryAvailable && isEASBuild) {
        console.log('🎤 ✅ Starting native voice recognition in EAS build');
        try {
          return await this.startNativeVoiceRecognition();
        } catch (nativeError) {
          console.error('❌ Native voice recognition failed:', nativeError);
          // Fall through to web speech API
        }
      } else if (Platform.OS !== 'web' && !isEASBuild) {
        console.log('⚠️ Native voice recognition not available - not in EAS build');
        console.log('💡 Voice recognition requires EAS development client build');
        this.callbacks.onError?.('Voice recognition requires an EAS development client build. Please rebuild with: eas build --profile preview --platform android');
        return false;
      }

      // Fallback to Web Speech API (web browsers)
      if (this.isWebSpeechAvailable()) {
        console.log('🎤 ✅ Starting Web Speech API voice recognition');
        return this.startWebSpeechRecognition();
      }

      // No voice recognition available
      console.error('❌ Voice recognition not available on this platform');
      console.log('🎤 Debug info:');
      console.log('   - Platform:', Platform.OS);
      console.log('   - Voice object:', !!Voice);
      console.log('   - Library available:', isVoiceLibraryAvailable);
      console.log('   - EAS Build:', isEASBuild);
      console.log('   - Native initialized:', this.isNativeVoiceInitialized);

      if (Platform.OS === 'web') {
        this.callbacks.onError?.('Voice recognition not supported in this browser. Please use Chrome, Safari, or Edge.');
      } else {
        // For native builds, provide specific guidance based on build type
        const isDevClient = isDevelopmentClient();
        console.log('🔍 Is development client:', isDevClient);

        if (isDevClient) {
          this.callbacks.onError?.('Voice recognition library failed to initialize. Please check microphone permissions in device settings.');
        } else {
          this.callbacks.onError?.('Voice recognition requires an EAS development client build. Please rebuild with: eas build --profile preview --platform android');
        }
      }

      return false;
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      this.callbacks.onError?.('Failed to start voice recognition');
      return false;
    }
  }

  private static async startNativeVoiceRecognition(): Promise<boolean> {
    try {
      if (!Voice) {
        console.error('❌ Native voice library not available');
        return false;
      }

      // Check if already listening
      const isAvailable = await Voice.isAvailable();
      if (!isAvailable) {
        console.error('❌ Native voice recognition not available on device');
        this.callbacks.onError?.('Voice recognition not available on this device');
        return false;
      }

      // Start listening
      await Voice.start('en-US');
      console.log('🎤 Native voice recognition started');
      return true;
    } catch (error) {
      console.error('❌ Failed to start native voice recognition:', error);
      this.callbacks.onError?.('Failed to start voice recognition');
      return false;
    }
  }

  private static isWebSpeechAvailable(): boolean {
    try {
      // Check if we're in a browser environment with Web Speech API
      return (
        typeof window !== 'undefined' &&
        ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window)
      );
    } catch {
      return false;
    }
  }

  private static isBrowserEnvironment(): boolean {
    try {
      // Check if we're in any browser-like environment
      return (
        typeof window !== 'undefined' &&
        typeof navigator !== 'undefined' &&
        typeof document !== 'undefined'
      );
    } catch {
      return false;
    }
  }

  private static startWebSpeechRecognition(): boolean {
    try {
      // @ts-ignore - Web Speech API types
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

      if (!SpeechRecognition) {
        console.error('❌ Web Speech API not supported in this browser');
        console.log('💡 Try Chrome, Safari, or Edge for voice recognition');
        this.callbacks.onError?.('Voice recognition not supported in this browser. Try Chrome or Safari.');
        return false;
      }

      console.log('✅ REAL Web Speech API detected - starting voice recognition');

      const recognition = new SpeechRecognition();

      // Configure for optimal performance
      recognition.continuous = false;           // Stop after one phrase
      recognition.interimResults = true;       // Show partial results
      recognition.lang = 'en-US';             // English language
      recognition.maxAlternatives = 1;        // Only best result

      // Store recognition instance for cleanup
      this.currentRecognition = recognition;

      recognition.onstart = () => {
        console.log('🎤 ✅ REAL voice recognition started successfully');
        this.isListening = true;
        this.callbacks.onStart?.();
      };

      recognition.onresult = (event: any) => {
        console.log('🎤 📝 Processing voice input...');

        const results = event.results;
        const lastResult = results[results.length - 1];
        const transcript = lastResult[0].transcript.trim();
        const confidence = lastResult[0].confidence || 0.9;
        const isFinal = lastResult.isFinal;

        console.log(`🎤 Voice transcript: "${transcript}" (confidence: ${Math.round(confidence * 100)}%, final: ${isFinal})`);

        if (isFinal) {
          console.log('✅ Final voice result:', transcript);
          this.callbacks.onResult?.({
            text: transcript,
            confidence,
            isFinal: true
          });
          this.isListening = false;
        } else {
          // Show partial results in real-time
          this.callbacks.onPartialResult?.(transcript);
        }
      };

      recognition.onend = () => {
        console.log('🎤 Voice recognition session ended');
        this.isListening = false;
        this.callbacks.onEnd?.();
        this.currentRecognition = null;
      };

      recognition.onerror = (event: any) => {
        console.error('🎤 ❌ Voice recognition error:', event.error);
        this.isListening = false;

        let errorMessage = 'Voice recognition failed';
        switch (event.error) {
          case 'no-speech':
            errorMessage = 'No speech detected. Please try again.';
            break;
          case 'audio-capture':
            errorMessage = 'Microphone not accessible. Please check permissions.';
            break;
          case 'not-allowed':
            errorMessage = 'Microphone permission denied. Please allow microphone access.';
            break;
          case 'network':
            errorMessage = 'Network error. Please check your connection.';
            break;
          default:
            errorMessage = `Voice recognition error: ${event.error}`;
        }

        this.callbacks.onError?.(errorMessage);
        this.currentRecognition = null;
      };

      // Start recognition
      recognition.start();
      console.log('🎤 Voice recognition request sent to browser...');
      return true;

    } catch (error) {
      console.error('❌ Failed to start Web Speech API:', error);
      this.callbacks.onError?.('Failed to initialize voice recognition');
      return false;
    }
  }

  // NO DEMO MODE - ONLY REAL VOICE RECOGNITION

  static async stopListening(): Promise<void> {
    try {
      console.log('🎤 Stopping voice recognition...');

      // Stop native voice recognition if active
      if (Voice && Platform.OS !== 'web' && this.isListening) {
        await Voice.stop();
        console.log('🎤 Native voice recognition stopped');
      }

      // Stop Web Speech API if active
      if (this.currentRecognition) {
        this.currentRecognition.stop();
        this.currentRecognition = null;
        console.log('🎤 Web Speech API stopped');
      }

      this.isListening = false;
      this.callbacks.onEnd?.();
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
    }
  }

  static async cancelListening(): Promise<void> {
    try {
      console.log('🎤 Fallback: Voice recognition cancelled');
      this.isListening = false;
      this.callbacks.onEnd?.();
    } catch (error) {
      console.error('Error canceling voice recognition:', error);
    }
  }

  static isCurrentlyListening(): boolean {
    return this.isListening;
  }

  // Fallback event handlers (not used in Expo Go version)
  // These would be used in a production build with native voice recognition

  // Utility methods
  static async checkPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        // Android permissions are handled automatically by the library
        return true;
      } else if (Platform.OS === 'ios') {
        // iOS permissions are handled automatically by the library
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking voice permissions:', error);
      return false;
    }
  }

  static async requestPermissions(): Promise<boolean> {
    try {
      // Permissions are requested automatically when starting voice recognition
      return await this.checkPermissions();
    } catch (error) {
      console.error('Error requesting voice permissions:', error);
      return false;
    }
  }

  // Text-to-speech functionality (for voice responses)
  static async speak(text: string): Promise<void> {
    try {
      // Note: For TTS, you might want to use a different library like react-native-tts
      // This is a placeholder for future TTS implementation
      console.log('🔊 Would speak:', text);
      
      // For now, we'll just log the text
      // In a full implementation, you'd use:
      // import Tts from 'react-native-tts';
      // await Tts.speak(text);
    } catch (error) {
      console.error('Error speaking text:', error);
    }
  }

  // Clean up resources
  static async destroy(): Promise<void> {
    try {
      console.log('🎤 Cleaning up voice service...');

      // Stop any active recognition
      await this.stopListening();

      // Clean up native voice if available
      if (Voice && Platform.OS !== 'web') {
        try {
          await Voice.destroy();
          console.log('🎤 Native voice service destroyed');
        } catch (error) {
          console.error('Error destroying native voice:', error);
        }
      }

      this.isListening = false;
      this.callbacks = {};
      this.currentRecognition = null;
      this.isNativeVoiceInitialized = false;

      console.log('✅ Voice service cleanup complete');
    } catch (error) {
      console.error('Error destroying voice service:', error);
    }
  }

  // Voice command processing
  static processVoiceCommand(text: string): {
    intent: string;
    entities: Record<string, any>;
    confidence: number;
  } {
    const lowerText = text.toLowerCase().trim();
    
    // Simple intent recognition (in production, you'd use a more sophisticated NLP service)
    if (lowerText.includes('scan') || lowerText.includes('analyze') || lowerText.includes('photo')) {
      return {
        intent: 'scan_food',
        entities: {},
        confidence: 0.8
      };
    }
    
    if (lowerText.includes('recipe') || lowerText.includes('cook') || lowerText.includes('make')) {
      const foodMatch = lowerText.match(/recipe for (.+)|make (.+)|cook (.+)/);
      return {
        intent: 'find_recipe',
        entities: {
          food: foodMatch ? (foodMatch[1] || foodMatch[2] || foodMatch[3]) : null
        },
        confidence: 0.8
      };
    }
    
    if (lowerText.includes('meal plan') || lowerText.includes('plan') || lowerText.includes('week')) {
      return {
        intent: 'create_meal_plan',
        entities: {},
        confidence: 0.8
      };
    }
    
    if (lowerText.includes('nutrition') || lowerText.includes('calories') || lowerText.includes('protein')) {
      return {
        intent: 'nutrition_question',
        entities: {
          question: text
        },
        confidence: 0.9
      };
    }
    
    // Default to general question
    return {
      intent: 'general_question',
      entities: {
        question: text
      },
      confidence: 0.6
    };
  }
}

export default VoiceService;
