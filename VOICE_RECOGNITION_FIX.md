# Voice Recognition Fix for EAS Builds

## Problem
Voice recognition was not working in EAS preview builds, showing error messages about requiring development builds.

## Root Cause
The EAS `preview` profile in `eas.json` was missing the `developmentClient: true` flag, which is required for native libraries like `@react-native-voice/voice` to function properly.

## Solution Applied

### 1. Updated EAS Configuration
**File: `eas.json`**
```json
{
  "build": {
    "preview": {
      "developmentClient": true,  // ← Added this line
      "distribution": "internal",
      "channel": "preview",
      // ... rest of config
    }
  }
}
```

### 2. Updated Error Messages
**File: `src/services/VoiceService.ts`**
- Updated error messages to provide specific rebuild instructions
- Changed from generic "development build" to specific EAS command

**File: `src/screens/RecipesScreenModern.tsx`**
- Updated voice recognition unavailable alert with correct rebuild command

### 3. Added Rebuild Script
**File: `scripts/rebuild-voice-support.js`**
- Automated script to check prerequisites and rebuild with voice support
- Validates EAS CLI, configuration, and dependencies
- Provides step-by-step guidance

**File: `package.json`**
- Added `rebuild:voice` script for easy access

## How to Fix Your Build

### Option 1: Use the Rebuild Script (Recommended)
```bash
npm run rebuild:voice
```

### Option 2: Manual Rebuild
```bash
eas build --profile preview --platform android
```

## What Changed
- **Before**: Preview builds were standard APKs without development client features
- **After**: Preview builds now include development client capabilities, enabling native voice recognition

## Verification
After installing the new APK:
1. Open Ask AI screen
2. Tap the microphone button
3. Voice recognition should start without error messages
4. Same functionality works in Recipe search

## Technical Details
- `@react-native-voice/voice` requires development client environment
- Development client provides access to native APIs not available in standard builds
- The `developmentClient: true` flag enables this environment in EAS builds
- This change maintains all other preview build characteristics (internal distribution, release configuration, etc.)

## Next Steps
1. Rebuild your app with the updated configuration
2. Install the new APK
3. Test voice recognition in Ask AI and Recipe search
4. Voice recognition should now work seamlessly!

## Troubleshooting
If voice recognition still doesn't work after rebuilding:
1. Ensure microphone permissions are granted
2. Check device compatibility (Android 6.0+ recommended)
3. Verify internet connection for speech processing
4. Try restarting the app after granting permissions
