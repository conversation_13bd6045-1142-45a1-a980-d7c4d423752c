import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import MultiSelectCard from '../../components/onboarding/MultiSelectCard';

type CuisinePreferencesScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'CuisinePreferences'>;

const CuisinePreferencesScreen: React.FC = () => {
  const navigation = useNavigation<CuisinePreferencesScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [selectedCuisines, setSelectedCuisines] = useState<string[]>(data.cuisinePreferences);

  const cuisineOptions = [
    { id: 'italian', label: 'Italian', description: 'Pasta, pizza, Mediterranean flavors', icon: 'restaurant' as const, color: '#10B981' },
    { id: 'asian', label: 'Asian', description: 'Chinese, Japanese, Thai, Korean', icon: 'leaf' as const, color: '#F59E0B' },
    { id: 'mexican', label: 'Mexican', description: 'Tacos, burritos, spicy flavors', icon: 'flame' as const, color: '#EF4444' },
    { id: 'indian', label: 'Indian', description: 'Curry, spices, vegetarian options', icon: 'flower' as const, color: '#8B5CF6' },
    { id: 'american', label: 'American', description: 'Burgers, BBQ, comfort food', icon: 'fast-food' as const, color: '#3B82F6' },
    { id: 'mediterranean', label: 'Mediterranean', description: 'Greek, Turkish, Middle Eastern', icon: 'water' as const, color: '#06B6D4' },
    { id: 'french', label: 'French', description: 'Classic techniques, rich flavors', icon: 'wine' as const, color: '#EC4899' },
    { id: 'healthy', label: 'Health-Focused', description: 'Clean eating, superfoods', icon: 'fitness' as const, color: '#84CC16' },
  ];

  const handleNext = () => {
    updateData('cuisinePreferences', selectedCuisines);
    nextStep();
    navigation.navigate('ActivityLevel');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  return (
    <OnboardingLayout
      title="Cuisine Preferences"
      subtitle="What types of food do you enjoy most?"
      onNext={handleNext}
      onBack={handleBack}
      showSkip={true}
      onSkip={handleNext}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <MultiSelectCard
          title="Select your favorite cuisines"
          options={cuisineOptions}
          selectedValues={selectedCuisines}
          onSelectionChange={setSelectedCuisines}
          allowMultiple={true}
        />
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    paddingTop: 16,
  },
});

export default CuisinePreferencesScreen;
