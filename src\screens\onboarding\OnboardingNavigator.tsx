import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { Platform } from 'react-native';

// Onboarding Screens
import WelcomeScreen from './WelcomeScreen';
import PersonalInfoScreen from './PersonalInfoScreen';
import HealthGoalsScreen from './HealthGoalsScreen';
import DietaryPreferencesScreen from './DietaryPreferencesScreen';
import AllergensScreen from './AllergensScreen';
import CuisinePreferencesScreen from './CuisinePreferencesScreen';
import ActivityLevelScreen from './ActivityLevelScreen';
import NutritionGoalsScreen from './NutritionGoalsScreen';
import NotificationSettingsScreen from './NotificationSettingsScreen';
import ProfilePictureScreen from './ProfilePictureScreen';
import CompletionScreen from './CompletionScreen';

export type OnboardingStackParamList = {
  Welcome: undefined;
  PersonalInfo: undefined;
  HealthGoals: undefined;
  DietaryPreferences: undefined;
  Allergens: undefined;
  CuisinePreferences: undefined;
  ActivityLevel: undefined;
  NutritionGoals: undefined;
  NotificationSettings: undefined;
  ProfilePicture: undefined;
  Completion: undefined;
};

const Stack = createStackNavigator<OnboardingStackParamList>();

const OnboardingNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="Welcome"
      screenOptions={{
        headerShown: false,
        gestureEnabled: Platform.OS === 'ios',
        gestureDirection: 'horizontal',
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name="PersonalInfo" component={PersonalInfoScreen} />
      <Stack.Screen name="HealthGoals" component={HealthGoalsScreen} />
      <Stack.Screen name="DietaryPreferences" component={DietaryPreferencesScreen} />
      <Stack.Screen name="Allergens" component={AllergensScreen} />
      <Stack.Screen name="CuisinePreferences" component={CuisinePreferencesScreen} />
      <Stack.Screen name="ActivityLevel" component={ActivityLevelScreen} />
      <Stack.Screen name="NutritionGoals" component={NutritionGoalsScreen} />
      <Stack.Screen name="NotificationSettings" component={NotificationSettingsScreen} />
      <Stack.Screen name="ProfilePicture" component={ProfilePictureScreen} />
      <Stack.Screen name="Completion" component={CompletionScreen} />
    </Stack.Navigator>
  );
};

export default OnboardingNavigator;
