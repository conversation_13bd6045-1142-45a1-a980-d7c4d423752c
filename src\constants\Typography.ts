import { StyleSheet } from 'react-native';

// Nutri AI Typography System
// Modern, health-focused typography with excellent readability

export const Typography = StyleSheet.create({
  // Display Typography - For hero sections and major headings
  displayLarge: {
    fontSize: 42,
    fontWeight: '900',
    letterSpacing: -1.5,
    lineHeight: 48,
    color: '#6B7C5A',
  },
  displayMedium: {
    fontSize: 36,
    fontWeight: '800',
    letterSpacing: -1,
    lineHeight: 42,
    color: '#6B7C5A',
  },
  displaySmall: {
    fontSize: 30,
    fontWeight: '700',
    letterSpacing: -0.5,
    lineHeight: 36,
    color: '#6B7C5A',
  },

  // Heading Typography - For section headers
  headingLarge: {
    fontSize: 28,
    fontWeight: '700',
    letterSpacing: -0.5,
    lineHeight: 34,
    color: '#1F2937',
  },
  headingMedium: {
    fontSize: 24,
    fontWeight: '600',
    letterSpacing: -0.25,
    lineHeight: 30,
    color: '#1F2937',
  },
  headingSmall: {
    fontSize: 20,
    fontWeight: '600',
    letterSpacing: 0,
    lineHeight: 26,
    color: '#1F2937',
  },

  // Title Typography - For card titles and important labels
  titleLarge: {
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0,
    lineHeight: 24,
    color: '#374151',
  },
  titleMedium: {
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.1,
    lineHeight: 22,
    color: '#374151',
  },
  titleSmall: {
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.1,
    lineHeight: 20,
    color: '#374151',
  },

  // Body Typography - For main content
  bodyLarge: {
    fontSize: 16,
    fontWeight: '400',
    letterSpacing: 0.15,
    lineHeight: 24,
    color: '#4B5563',
  },
  bodyMedium: {
    fontSize: 14,
    fontWeight: '400',
    letterSpacing: 0.25,
    lineHeight: 20,
    color: '#4B5563',
  },
  bodySmall: {
    fontSize: 12,
    fontWeight: '400',
    letterSpacing: 0.4,
    lineHeight: 16,
    color: '#4B5563',
  },

  // Label Typography - For form labels and small text
  labelLarge: {
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.1,
    lineHeight: 20,
    color: '#6B7280',
  },
  labelMedium: {
    fontSize: 12,
    fontWeight: '500',
    letterSpacing: 0.5,
    lineHeight: 16,
    color: '#6B7280',
  },
  labelSmall: {
    fontSize: 10,
    fontWeight: '500',
    letterSpacing: 0.5,
    lineHeight: 14,
    color: '#6B7280',
  },

  // Brand Typography - For Nutri AI branding
  brandLogo: {
    fontSize: 28,
    fontWeight: '900',
    letterSpacing: -1,
    color: '#6B7C5A',
  },
  brandLogoSmall: {
    fontSize: 18,
    fontWeight: '900',
    letterSpacing: -0.5,
    color: '#6B7C5A',
  },
  brandTagline: {
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.5,
    color: '#8B9A7A',
  },

  // Accent Typography - For AI and special elements
  aiAccent: {
    fontWeight: '300',
    fontStyle: 'italic',
  },
  
  // Nutrition-specific Typography
  nutritionValue: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.25,
    color: '#6B7C5A',
  },
  nutritionLabel: {
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.1,
    color: '#374151',
  },
  nutritionUnit: {
    fontSize: 12,
    fontWeight: '400',
    letterSpacing: 0.25,
    color: '#6B7280',
  },

  // Interactive Typography - For buttons and links
  buttonLarge: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.1,
    color: '#fcf4ec',
  },
  buttonMedium: {
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.25,
    color: '#fcf4ec',
  },
  buttonSmall: {
    fontSize: 12,
    fontWeight: '500',
    letterSpacing: 0.5,
    color: '#fcf4ec',
  },
  
  link: {
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.1,
    color: '#6B7C5A',
    textDecorationLine: 'underline',
  },

  // Status Typography - For success, error, warning states
  success: {
    color: '#059669',
  },
  error: {
    color: '#DC2626',
  },
  warning: {
    color: '#D97706',
  },
  info: {
    color: '#2563EB',
  },

  // Light variants for dark backgrounds
  light: {
    color: '#fcf4ec',
  },
  lightMuted: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  lightSubtle: {
    color: 'rgba(255, 255, 255, 0.6)',
  },
});

// Typography utility functions
export const getTypographyStyle = (variant: keyof typeof Typography) => {
  return Typography[variant];
};

export const combineTypographyStyles = (...styles: any[]) => {
  return StyleSheet.flatten(styles);
};
