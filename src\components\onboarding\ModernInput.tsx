import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  SlideInDown,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

interface ModernInputProps {
  label: string;
  value: string | number;
  onChangeText: (text: string) => void;
  placeholder?: string;
  keyboardType?: 'default' | 'numeric' | 'email-address';
  suffix?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  multiline?: boolean;
  maxLength?: number;
}

interface ModernSelectProps {
  label: string;
  value: string;
  onSelect: (value: string) => void;
  options: { label: string; value: string }[];
  placeholder?: string;
  icon?: keyof typeof Ionicons.glyphMap;
}

export const ModernInput: React.FC<ModernInputProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  keyboardType = 'default',
  suffix,
  icon,
  multiline = false,
  maxLength,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handleFocus = () => {
    setIsFocused(true);
    // Removed scale animation to prevent form field expansion
    // scale.value = withSpring(1.02);
  };

  const handleBlur = () => {
    setIsFocused(false);
    // scale.value = withSpring(1);
  };

  return (
    <Animated.View style={[styles.inputContainer, animatedStyle]}>
      <Text style={styles.inputLabel}>{label}</Text>
      <View style={[styles.inputWrapper, isFocused && styles.inputWrapperFocused]}>
        {icon && (
          <View style={styles.inputIcon}>
            <Ionicons name={icon} size={20} color="#6B7C5A" />
          </View>
        )}
        <TextInput
          style={[styles.textInput, multiline && styles.textInputMultiline]}
          value={value.toString()}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor="rgba(107, 124, 90, 0.5)"
          keyboardType={keyboardType}
          onFocus={handleFocus}
          onBlur={handleBlur}
          multiline={multiline}
          maxLength={maxLength}
        />
        {suffix && (
          <View style={styles.inputSuffix}>
            <Text style={styles.suffixText}>{suffix}</Text>
          </View>
        )}
      </View>
    </Animated.View>
  );
};

export const ModernSelect: React.FC<ModernSelectProps> = ({
  label,
  value,
  onSelect,
  options,
  placeholder,
  icon,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePress = () => {
    scale.value = withSpring(0.98);
    setTimeout(() => {
      scale.value = withSpring(1);
      setIsVisible(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }, 100);
  };

  const handleSelect = (selectedValue: string) => {
    onSelect(selectedValue);
    setIsVisible(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
  };

  const selectedOption = options.find(option => option.value === value);

  return (
    <>
      <Animated.View style={[styles.inputContainer, animatedStyle]}>
        <Text style={styles.inputLabel}>{label}</Text>
        <TouchableOpacity style={styles.selectWrapper} onPress={handlePress}>
          {icon && (
            <View style={styles.inputIcon}>
              <Ionicons name={icon} size={20} color="#6B7C5A" />
            </View>
          )}
          <Text style={[styles.selectText, !selectedOption && styles.selectPlaceholder]}>
            {selectedOption ? selectedOption.label : placeholder}
          </Text>
          <Ionicons name="chevron-down" size={20} color="#6B7C5A" />
        </TouchableOpacity>
      </Animated.View>

      <Modal visible={isVisible} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            onPress={() => setIsVisible(false)}
          />
          <Animated.View entering={SlideInDown.duration(300)} style={styles.modalContent}>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{label}</Text>
                <TouchableOpacity onPress={() => setIsVisible(false)}>
                  <Ionicons name="close" size={24} color="#6B7C5A" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.optionsList}>
                {options.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.optionItem,
                      option.value === value && styles.optionItemSelected
                    ]}
                    onPress={() => handleSelect(option.value)}
                  >
                    <Text style={[
                      styles.optionText,
                      option.value === value && styles.optionTextSelected
                    ]}>
                      {option.label}
                    </Text>
                    {option.value === value && (
                      <Ionicons name="checkmark" size={20} color="#fcf4ec" />
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fcf4ec',
    borderRadius: 24,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  inputWrapperFocused: {
    borderColor: '#6B7C5A',
    backgroundColor: '#fcf4ec',
  },
  inputIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  textInputMultiline: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  inputSuffix: {
    marginLeft: 12,
  },
  suffixText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  selectWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fcf4ec',
    borderRadius: 24,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  selectText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  selectPlaceholder: {
    color: '#9CA3AF',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    width: '90%',
    maxHeight: '70%',
    borderRadius: 32,
    overflow: 'hidden',
    backgroundColor: '#fcf4ec',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  modalContainer: {
    backgroundColor: '#fcf4ec',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B7C5A',
  },
  optionsList: {
    maxHeight: 300,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  optionItemSelected: {
    backgroundColor: '#8B9A7A',
  },
  optionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  optionTextSelected: {
    color: '#fcf4ec',
    fontWeight: '700',
  },
});

export default { ModernInput, ModernSelect };
