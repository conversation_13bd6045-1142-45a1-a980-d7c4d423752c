import AsyncStorage from '@react-native-async-storage/async-storage';

export interface FavoriteRecipe {
  id: string;
  name: string;
  imageUrl?: string;
  cookingTime?: string;
  difficulty?: string;
  calories?: number;
  protein?: number;
  dateAdded: number;
  source?: 'generated' | 'weekly_plan' | 'manual';
}

class FavoritesService {
  private static instance: FavoritesService;
  private readonly FAVORITES_KEY = 'favorite_recipes';

  static getInstance(): FavoritesService {
    if (!FavoritesService.instance) {
      FavoritesService.instance = new FavoritesService();
    }
    return FavoritesService.instance;
  }

  // Add recipe to favorites
  async addToFavorites(recipe: Omit<FavoriteRecipe, 'dateAdded'>): Promise<boolean> {
    try {
      console.log('❤️ Adding recipe to favorites:', recipe.name);
      
      const favorites = await this.getFavorites();
      
      // Check if already exists
      const existingIndex = favorites.findIndex(fav => fav.id === recipe.id);
      if (existingIndex !== -1) {
        console.log('⚠️ Recipe already in favorites');
        return true;
      }
      
      // Add new favorite
      const newFavorite: FavoriteRecipe = {
        ...recipe,
        dateAdded: Date.now()
      };
      
      favorites.unshift(newFavorite); // Add to beginning for recent-first order
      
      await AsyncStorage.setItem(this.FAVORITES_KEY, JSON.stringify(favorites));
      console.log('✅ Recipe added to favorites successfully');
      
      return true;
    } catch (error) {
      console.error('❌ Error adding recipe to favorites:', error);
      return false;
    }
  }

  // Remove recipe from favorites
  async removeFromFavorites(recipeId: string): Promise<boolean> {
    try {
      console.log('💔 Removing recipe from favorites:', recipeId);
      
      const favorites = await this.getFavorites();
      const filteredFavorites = favorites.filter(fav => fav.id !== recipeId);
      
      await AsyncStorage.setItem(this.FAVORITES_KEY, JSON.stringify(filteredFavorites));
      console.log('✅ Recipe removed from favorites successfully');
      
      return true;
    } catch (error) {
      console.error('❌ Error removing recipe from favorites:', error);
      return false;
    }
  }

  // Check if recipe is favorited
  async isFavorited(recipeId: string): Promise<boolean> {
    try {
      const favorites = await this.getFavorites();
      return favorites.some(fav => fav.id === recipeId);
    } catch (error) {
      console.error('❌ Error checking if recipe is favorited:', error);
      return false;
    }
  }

  // Get all favorites with pagination
  async getFavorites(page: number = 1, limit: number = 20): Promise<FavoriteRecipe[]> {
    try {
      const favoritesJson = await AsyncStorage.getItem(this.FAVORITES_KEY);
      if (!favoritesJson) {
        return [];
      }
      
      const allFavorites: FavoriteRecipe[] = JSON.parse(favoritesJson);
      
      // Apply pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      return allFavorites.slice(startIndex, endIndex);
    } catch (error) {
      console.error('❌ Error getting favorites:', error);
      return [];
    }
  }

  // Get total count of favorites
  async getFavoritesCount(): Promise<number> {
    try {
      const favoritesJson = await AsyncStorage.getItem(this.FAVORITES_KEY);
      if (!favoritesJson) {
        return 0;
      }
      
      const favorites: FavoriteRecipe[] = JSON.parse(favoritesJson);
      return favorites.length;
    } catch (error) {
      console.error('❌ Error getting favorites count:', error);
      return 0;
    }
  }

  // Clear all favorites
  async clearAllFavorites(): Promise<boolean> {
    try {
      await AsyncStorage.removeItem(this.FAVORITES_KEY);
      console.log('✅ All favorites cleared');
      return true;
    } catch (error) {
      console.error('❌ Error clearing favorites:', error);
      return false;
    }
  }

  // Toggle favorite status
  async toggleFavorite(recipe: Omit<FavoriteRecipe, 'dateAdded'>): Promise<boolean> {
    try {
      const isCurrentlyFavorited = await this.isFavorited(recipe.id);
      
      if (isCurrentlyFavorited) {
        return await this.removeFromFavorites(recipe.id);
      } else {
        return await this.addToFavorites(recipe);
      }
    } catch (error) {
      console.error('❌ Error toggling favorite:', error);
      return false;
    }
  }

  // Search favorites
  async searchFavorites(query: string): Promise<FavoriteRecipe[]> {
    try {
      const allFavorites = await this.getFavorites(1, 1000); // Get all for search

      if (!query.trim()) {
        return allFavorites;
      }

      const searchTerm = query.toLowerCase();
      return allFavorites.filter(recipe =>
        recipe.name.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('❌ Error searching favorites:', error);
      return [];
    }
  }

  // Test function to verify AsyncStorage integration
  async testFavoritesSystem(): Promise<boolean> {
    try {
      console.log('🧪 Testing Favorites System...');

      // Test recipe
      const testRecipe = {
        id: 'test_recipe_123',
        name: 'Test Grilled Chicken',
        imageUrl: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836',
        cookingTime: '25 min',
        difficulty: 'Easy',
        calories: 350,
        protein: 30,
        source: 'manual' as const
      };

      // Test 1: Add to favorites
      const addResult = await this.addToFavorites(testRecipe);
      if (!addResult) {
        console.error('❌ Test failed: Could not add to favorites');
        return false;
      }

      // Test 2: Check if favorited
      const isFavorited = await this.isFavorited(testRecipe.id);
      if (!isFavorited) {
        console.error('❌ Test failed: Recipe not found in favorites');
        return false;
      }

      // Test 3: Get favorites list
      const favorites = await this.getFavorites();
      const foundRecipe = favorites.find(fav => fav.id === testRecipe.id);
      if (!foundRecipe) {
        console.error('❌ Test failed: Recipe not in favorites list');
        return false;
      }

      // Test 4: Remove from favorites
      const removeResult = await this.removeFromFavorites(testRecipe.id);
      if (!removeResult) {
        console.error('❌ Test failed: Could not remove from favorites');
        return false;
      }

      // Test 5: Verify removal
      const isStillFavorited = await this.isFavorited(testRecipe.id);
      if (isStillFavorited) {
        console.error('❌ Test failed: Recipe still in favorites after removal');
        return false;
      }

      console.log('✅ Favorites System Test Passed! AsyncStorage integration working correctly.');
      return true;
    } catch (error) {
      console.error('❌ Favorites System Test Failed:', error);
      return false;
    }
  }
}

export default FavoritesService.getInstance();
