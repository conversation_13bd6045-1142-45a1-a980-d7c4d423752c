[{"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/CircularProgress.tsx", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type '() => { strokeDashoffset: number; }' is not assignable to parameter of type '() => AnimatedStyleProp<ViewStyle | TextStyle | ImageStyle>'.\n  Type '{ strokeDashoffset: number; }' is not assignable to type 'AnimatedStyleProp<ViewStyle | TextStyle | ImageStyle>'.", "source": "ts", "startLineNumber": 49, "startColumn": 42, "endLineNumber": 49, "endColumn": 49, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/CircularProgress.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type 'AnimatedStyleProp<ViewStyle | TextStyle | ImageStyle>' is not assignable to type 'Partial<AnimateProps<CircleProps>> | undefined'.\n  Type 'AnimateStyle<ViewStyle>' is not assignable to type 'Partial<AnimateProps<CircleProps>>'.\n    Types of property 'pointerEvents' are incompatible.\n      Type 'number | \"box-none\" | \"none\" | \"box-only\" | \"auto\" | undefined' is not assignable to type '\"box-none\" | \"none\" | \"box-only\" | \"auto\" | SharedValue<\"box-none\" | \"none\" | \"box-only\" | \"auto\" | undefined> | undefined'.\n        Type 'number' is not assignable to type '\"box-none\" | \"none\" | \"box-only\" | \"auto\" | SharedValue<\"box-none\" | \"none\" | \"box-only\" | \"auto\" | undefined> | undefined'.", "source": "ts", "startLineNumber": 89, "startColumn": 11, "endLineNumber": 89, "endColumn": 24, "relatedInformation": [{"startLineNumber": 101, "startColumn": 7, "endLineNumber": 101, "endColumn": 20, "message": "The expected type comes from property 'animatedProps' which is declared here on type 'IntrinsicAttributes & IntrinsicClassAttributes<Component<AnimateProps<CircleProps>, any, any>> & Readonly<...>'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/node_modules/react-native-reanimated/react-native-reanimated.d.ts"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/GestureComponents.tsx", "owner": "typescript", "code": "2305", "severity": 8, "message": "Module '\"expo-haptics\"' has no exported member 'Haptics'.", "source": "ts", "startLineNumber": 20, "startColumn": 10, "endLineNumber": 20, "endColumn": 17, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/GestureComponents.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type 'OnGestureEvent<PanGestureHandlerGestureEvent>' is not assignable to type '(event: GestureEvent<LongPressGestureHandlerEventPayload>) => void'.\n  Types of parameters 'event' and 'event' are incompatible.\n    Type 'GestureEvent<LongPressGestureHandlerEventPayload>' is not assignable to type 'PanGestureHandlerGestureEvent'.\n      Types of property 'nativeEvent' are incompatible.\n        Type 'Readonly<GestureEventPayload & LongPressGestureHandlerEventPayload>' is missing the following properties from type 'Readonly<GestureEventPayload & PanGestureHandlerEventPayload>': translationX, translationY, velocityX, velocityY", "source": "ts", "startLineNumber": 176, "startColumn": 30, "endLineNumber": 176, "endColumn": 44, "relatedInformation": [{"startLineNumber": 57, "startColumn": 5, "endLineNumber": 57, "endColumn": 19, "message": "The expected type comes from property 'onGestureEvent' which is declared here on type 'IntrinsicAttributes & LongPressGestureHandlerProps & RefAttributes<any>'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/node_modules/react-native-gesture-handler/lib/typescript/handlers/gestureHandlerCommon.d.ts"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/GestureComponents.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type 'OnGestureEvent<PanGestureHandlerGestureEvent>' is not assignable to type '(event: GestureEvent<TapGestureHandlerEventPayload>) => void'.\n  Types of parameters 'event' and 'event' are incompatible.\n    Type 'GestureEvent<TapGestureHandlerEventPayload>' is not assignable to type 'PanGestureHandlerGestureEvent'.\n      Types of property 'nativeEvent' are incompatible.\n        Type 'Readonly<GestureEventPayload & TapGestureHandlerEventPayload>' is missing the following properties from type 'Readonly<GestureEventPayload & PanGestureHandlerEventPayload>': translationX, translationY, velocityX, velocityY", "source": "ts", "startLineNumber": 177, "startColumn": 26, "endLineNumber": 177, "endColumn": 40, "relatedInformation": [{"startLineNumber": 57, "startColumn": 5, "endLineNumber": 57, "endColumn": 19, "message": "The expected type comes from property 'onGestureEvent' which is declared here on type 'IntrinsicAttributes & TapGestureHandlerProps & RefAttributes<any>'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/node_modules/react-native-gesture-handler/lib/typescript/handlers/gestureHandlerCommon.d.ts"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/GestureComponents.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'scale' does not exist on type 'Readonly<GestureEventPayload & PanGestureHandlerEventPayload>'.", "source": "ts", "startLineNumber": 252, "startColumn": 48, "endLineNumber": 252, "endColumn": 53, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/GestureComponents.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type 'OnGestureEvent<PanGestureHandlerGestureEvent>' is not assignable to type '(event: GestureEvent<PinchGestureHandlerEventPayload>) => void'.\n  Types of parameters 'event' and 'event' are incompatible.\n    Type 'GestureEvent<PinchGestureHandlerEventPayload>' is not assignable to type 'PanGestureHandlerGestureEvent'.\n      Types of property 'nativeEvent' are incompatible.\n        Type 'Readonly<GestureEventPayload & PinchGestureHandlerEventPayload>' is missing the following properties from type 'Readonly<GestureEventPayload & PanGestureHandlerEventPayload>': x, y, absoluteX, absoluteY, and 4 more.", "source": "ts", "startLineNumber": 272, "startColumn": 26, "endLineNumber": 272, "endColumn": 40, "relatedInformation": [{"startLineNumber": 57, "startColumn": 5, "endLineNumber": 57, "endColumn": 19, "message": "The expected type comes from property 'onGestureEvent' which is declared here on type 'IntrinsicAttributes & PinchGestureHandlerProps & RefAttributes<any>'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/node_modules/react-native-gesture-handler/lib/typescript/handlers/gestureHandlerCommon.d.ts"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/GestureComponents.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'rotation' does not exist on type 'Readonly<GestureEventPayload & PanGestureHandlerEventPayload>'.", "source": "ts", "startLineNumber": 294, "startColumn": 51, "endLineNumber": 294, "endColumn": 59, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/GestureComponents.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type 'OnGestureEvent<PanGestureHandlerGestureEvent>' is not assignable to type '(event: GestureEvent<RotationGestureHandlerEventPayload>) => void'.\n  Types of parameters 'event' and 'event' are incompatible.\n    Type 'GestureEvent<RotationGestureHandlerEventPayload>' is not assignable to type 'PanGestureHandlerGestureEvent'.\n      Types of property 'nativeEvent' are incompatible.\n        Type 'Readonly<GestureEventPayload & RotationGestureHandlerEventPayload>' is missing the following properties from type 'Readonly<GestureEventPayload & PanGestureHandlerEventPayload>': x, y, absoluteX, absoluteY, and 4 more.", "source": "ts", "startLineNumber": 312, "startColumn": 29, "endLineNumber": 312, "endColumn": 43, "relatedInformation": [{"startLineNumber": 57, "startColumn": 5, "endLineNumber": 57, "endColumn": 19, "message": "The expected type comes from property 'onGestureEvent' which is declared here on type 'IntrinsicAttributes & RotationGestureHandlerProps & RefAttributes<any>'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/node_modules/react-native-gesture-handler/lib/typescript/handlers/gestureHandlerCommon.d.ts"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/HealthMonitor.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'getCurrentHealthData' does not exist on type 'HealthService'.", "source": "ts", "startLineNumber": 93, "startColumn": 36, "endLineNumber": 93, "endColumn": 56, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/HealthMonitor.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'getCurrentSteps' does not exist on type 'HealthService'.", "source": "ts", "startLineNumber": 94, "startColumn": 35, "endLineNumber": 94, "endColumn": 50, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/HealthMonitor.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'getCurrentHeartRate' does not exist on type 'HealthService'.", "source": "ts", "startLineNumber": 101, "startColumn": 23, "endLineNumber": 101, "endColumn": 42, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/HealthMonitor.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'getBodyTemperature' does not exist on type 'HealthService'.", "source": "ts", "startLineNumber": 102, "startColumn": 23, "endLineNumber": 102, "endColumn": 41, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/HealthMonitor.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'getOxygenSaturation' does not exist on type 'HealthService'.", "source": "ts", "startLineNumber": 103, "startColumn": 23, "endLineNumber": 103, "endColumn": 42, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/HealthMonitor.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type '\"thermometer\"' is not assignable to type '\"timer\" | \"home\" | \"water\" | \"scan\" | \"error\" | \"success\" | \"loading\" | \"scanner\" | \"recipes\" | \"plan\" | \"profile\" | \"apple\" | \"carrot\" | \"avocado\" | \"broccoli\" | \"progressCircle\" | ... 9 more ... | \"chef\"'.", "source": "ts", "startLineNumber": 276, "startColumn": 19, "endLineNumber": 276, "endColumn": 23, "relatedInformation": [{"startLineNumber": 19, "startColumn": 3, "endLineNumber": 19, "endColumn": 7, "message": "The expected type comes from property 'name' which is declared here on type 'IntrinsicAttributes & LottieIconProps'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/LottieIcon.tsx"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/HealthMonitor.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type '\"heart\"' is not assignable to type '\"timer\" | \"home\" | \"water\" | \"scan\" | \"error\" | \"success\" | \"loading\" | \"scanner\" | \"recipes\" | \"plan\" | \"profile\" | \"apple\" | \"carrot\" | \"avocado\" | \"broccoli\" | \"progressCircle\" | ... 9 more ... | \"chef\"'.", "source": "ts", "startLineNumber": 309, "startColumn": 19, "endLineNumber": 309, "endColumn": 23, "relatedInformation": [{"startLineNumber": 19, "startColumn": 3, "endLineNumber": 19, "endColumn": 7, "message": "The expected type comes from property 'name' which is declared here on type 'IntrinsicAttributes & LottieIconProps'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/LottieIcon.tsx"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/HealthMonitor.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type '\"map\"' is not assignable to type '\"timer\" | \"home\" | \"water\" | \"scan\" | \"error\" | \"success\" | \"loading\" | \"scanner\" | \"recipes\" | \"plan\" | \"profile\" | \"apple\" | \"carrot\" | \"avocado\" | \"broccoli\" | \"progressCircle\" | ... 9 more ... | \"chef\"'.", "source": "ts", "startLineNumber": 363, "startColumn": 25, "endLineNumber": 363, "endColumn": 29, "relatedInformation": [{"startLineNumber": 19, "startColumn": 3, "endLineNumber": 19, "endColumn": 7, "message": "The expected type comes from property 'name' which is declared here on type 'IntrinsicAttributes & LottieIconProps'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/LottieIcon.tsx"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/LottieIcon.tsx", "owner": "typescript", "code": "2305", "severity": 8, "message": "Module '\"../constants/Colors\"' has no exported member 'AnimationConfig'.", "source": "ts", "startLineNumber": 16, "startColumn": 18, "endLineNumber": 16, "endColumn": 33, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/LottieIcon.tsx", "owner": "typescript", "code": "2769", "severity": 8, "message": "No overload matches this call.\n  Overload 1 of 2, '(props: AnimatedLottieViewProps): AnimatedLottieView', gave the following error.\n    Type '{ ref: RefObject<AnimatedLottieView>; source: any; style: ({} | { width: number; height: number; })[]; loop: boolean | undefined; autoPlay: boolean | undefined; speed: number; colorFilters: { ...; }[]; onPress: (() => void) | undefined; }' is not assignable to type 'IntrinsicAttributes & IntrinsicClassAttributes<AnimatedLottieView> & Readonly<AnimatedLottieViewProps>'.\n      Property 'onPress' does not exist on type 'IntrinsicAttributes & IntrinsicClassAttributes<AnimatedLottieView> & Readonly<AnimatedLottieViewProps>'.\n  Overload 2 of 2, '(props: AnimatedLottieViewProps, context: any): AnimatedLottieView', gave the following error.\n    Type '{ ref: RefObject<AnimatedLottieView>; source: any; style: ({} | { width: number; height: number; })[]; loop: boolean | undefined; autoPlay: boolean | undefined; speed: number; colorFilters: { ...; }[]; onPress: (() => void) | undefined; }' is not assignable to type 'IntrinsicAttributes & IntrinsicClassAttributes<AnimatedLottieView> & Readonly<AnimatedLottieViewProps>'.\n      Property 'onPress' does not exist on type 'IntrinsicAttributes & IntrinsicClassAttributes<AnimatedLottieView> & Readonly<AnimatedLottieViewProps>'.", "source": "ts", "startLineNumber": 108, "startColumn": 13, "endLineNumber": 108, "endColumn": 20, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernButton.tsx", "owner": "typescript", "code": "2305", "severity": 8, "message": "Module '\"../constants/Colors\"' has no exported member 'AnimationConfig'.", "source": "ts", "startLineNumber": 23, "startColumn": 74, "endLineNumber": 23, "endColumn": 89, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernButton.tsx", "owner": "typescript", "code": "2551", "severity": 8, "message": "Property 'xsButton' does not exist on type '{ buttonContainer: { position: \"relative\"; }; glowContainer: { borderRadius: number; }; button: { borderRadius: number; overflow: \"hidden\"; shadowColor: string; shadowOffset: { width: number; height: number; }; shadowOpacity: number; shadowRadius: number; elevation: number; }; ... 26 more ...; iconRight: { ...; }; }'. Did you mean 'smButton'?", "source": "ts", "startLineNumber": 138, "startColumn": 14, "endLineNumber": 138, "endColumn": 29, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernButton.tsx", "owner": "typescript", "code": "7053", "severity": 8, "message": "Element implicitly has an 'any' type because expression of type '\"primaryButton\" | \"secondaryButton\" | \"outlineButton\" | \"ghostButton\" | \"destructiveButton\" | \"glassButton\" | \"floatingButton\"' can't be used to index type '{ buttonContainer: { position: \"relative\"; }; glowContainer: { borderRadius: number; }; button: { borderRadius: number; overflow: \"hidden\"; shadowColor: string; shadowOffset: { width: number; height: number; }; shadowOpacity: number; shadowRadius: number; elevation: number; }; ... 26 more ...; iconRight: { ...; }; }'.\n  Property 'glassButton' does not exist on type '{ buttonContainer: { position: \"relative\"; }; glowContainer: { borderRadius: number; }; button: { borderRadius: number; overflow: \"hidden\"; shadowColor: string; shadowOffset: { width: number; height: number; }; shadowOpacity: number; shadowRadius: number; elevation: number; }; ... 26 more ...; iconRight: { ...; }; }'.", "source": "ts", "startLineNumber": 139, "startColumn": 7, "endLineNumber": 139, "endColumn": 33, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernButton.tsx", "owner": "typescript", "code": "2551", "severity": 8, "message": "Property 'xsText' does not exist on type '{ buttonContainer: { position: \"relative\"; }; glowContainer: { borderRadius: number; }; button: { borderRadius: number; overflow: \"hidden\"; shadowColor: string; shadowOffset: { width: number; height: number; }; shadowOpacity: number; shadowRadius: number; elevation: number; }; ... 26 more ...; iconRight: { ...; }; }'. Did you mean 'smText'?", "source": "ts", "startLineNumber": 150, "startColumn": 14, "endLineNumber": 150, "endColumn": 27, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernButton.tsx", "owner": "typescript", "code": "7053", "severity": 8, "message": "Element implicitly has an 'any' type because expression of type '\"primaryText\" | \"secondaryText\" | \"outlineText\" | \"ghostText\" | \"destructiveText\" | \"glassText\" | \"floatingText\"' can't be used to index type '{ buttonContainer: { position: \"relative\"; }; glowContainer: { borderRadius: number; }; button: { borderRadius: number; overflow: \"hidden\"; shadowColor: string; shadowOffset: { width: number; height: number; }; shadowOpacity: number; shadowRadius: number; elevation: number; }; ... 26 more ...; iconRight: { ...; }; }'.\n  Property 'glassText' does not exist on type '{ buttonContainer: { position: \"relative\"; }; glowContainer: { borderRadius: number; }; button: { borderRadius: number; overflow: \"hidden\"; shadowColor: string; shadowOffset: { width: number; height: number; }; shadowOpacity: number; shadowRadius: number; elevation: number; }; ... 26 more ...; iconRight: { ...; }; }'.", "source": "ts", "startLineNumber": 151, "startColumn": 7, "endLineNumber": 151, "endColumn": 31, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2305", "severity": 8, "message": "Module '\"../constants/Colors\"' has no exported member 'AnimationConfig'.", "source": "ts", "startLineNumber": 27, "startColumn": 74, "endLineNumber": 27, "endColumn": 89, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2551", "severity": 8, "message": "Property 'xsCard' does not exist on type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'. Did you mean 'smCard'?", "source": "ts", "startLineNumber": 175, "startColumn": 14, "endLineNumber": 175, "endColumn": 27, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "7053", "severity": 8, "message": "Element implicitly has an 'any' type because expression of type '\"defaultCard\" | \"glassCard\" | \"gradientCard\" | \"elevatedCard\" | \"minimalCard\" | \"heroCard\" | \"glassMorphismCard\" | \"premiumCard\" | \"floatingCard\"' can't be used to index type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'.\n  Property 'glassMorphismCard' does not exist on type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'.", "source": "ts", "startLineNumber": 176, "startColumn": 7, "endLineNumber": 176, "endColumn": 31, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2551", "severity": 8, "message": "Property 'fullBorderRadius' does not exist on type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'. Did you mean 'lgBorderRadius'?", "source": "ts", "startLineNumber": 177, "startColumn": 14, "endLineNumber": 177, "endColumn": 43, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "7053", "severity": 8, "message": "Element implicitly has an 'any' type because expression of type '`${string}Shadow`' can't be used to index type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'.", "source": "ts", "startLineNumber": 178, "startColumn": 7, "endLineNumber": 178, "endColumn": 41, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2551", "severity": 8, "message": "Property 'glassMorphismIconContainer' does not exist on type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'. Did you mean 'glassIconContainer'?", "source": "ts", "startLineNumber": 187, "startColumn": 52, "endLineNumber": 187, "endColumn": 77, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2551", "severity": 8, "message": "Property 'xsTitle' does not exist on type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'. Did you mean 'smTitle'?", "source": "ts", "startLineNumber": 197, "startColumn": 44, "endLineNumber": 197, "endColumn": 58, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "7053", "severity": 8, "message": "Element implicitly has an 'any' type because expression of type '\"glassTitle\" | \"gradientTitle\" | \"heroTitle\" | \"defaultTitle\" | \"minimalTitle\" | \"glassMorphismTitle\" | \"elevatedTitle\" | \"premiumTitle\" | \"floatingTitle\"' can't be used to index type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'.\n  Property 'defaultTitle' does not exist on type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'.", "source": "ts", "startLineNumber": 197, "startColumn": 61, "endLineNumber": 197, "endColumn": 86, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2551", "severity": 8, "message": "Property 'xsDescription' does not exist on type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'. Did you mean 'smDescription'?", "source": "ts", "startLineNumber": 201, "startColumn": 52, "endLineNumber": 201, "endColumn": 72, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2551", "severity": 8, "message": "Property 'defaultDescription' does not exist on type '{ card: { overflow: \"hidden\"; position: \"relative\"; }; smCard: { padding: number; minHeight: number; }; mdCard: { padding: number; minHeight: number; }; lgCard: { padding: number; minHeight: number; }; xlCard: { ...; }; ... 45 more ...; disabled: { ...; }; }'. Did you mean 'mdDescription'?", "source": "ts", "startLineNumber": 201, "startColumn": 82, "endLineNumber": 201, "endColumn": 105, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glass' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 328, "startColumn": 29, "endLineNumber": 328, "endColumn": 34, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernCard.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glassBorder' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 330, "startColumn": 25, "endLineNumber": 330, "endColumn": 36, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernInput.tsx", "owner": "typescript", "code": "2769", "severity": 8, "message": "No overload matches this call.\n  Overload 1 of 2, '(props: AnimateProps<ViewProps>): View', gave the following error.\n    Type '(false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined)[]' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n      Type '(false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined)[]' is not assignable to type 'RecursiveArray<false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | RegisteredStyle<...> | null | undefined>'.\n        The types returned by 'pop()' are incompatible between these types.\n          Type 'false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n            Type '\"\"' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n  Overload 2 of 2, '(props: AnimateProps<ViewProps>, context: any): View', gave the following error.\n    Type '(false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined)[]' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n      Type '(false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined)[]' is not assignable to type 'RecursiveArray<false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | RegisteredStyle<...> | null | undefined>'.\n        The types returned by 'pop()' are incompatible between these types.\n          Type 'false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n            Type '\"\"' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.", "source": "ts", "startLineNumber": 195, "startColumn": 34, "endLineNumber": 195, "endColumn": 53, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernInput.tsx", "owner": "typescript", "code": "2769", "severity": 8, "message": "No overload matches this call.\n  Overload 1 of 2, '(props: ViewProps): View', gave the following error.\n    Type '(false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined)[]' is not assignable to type 'StyleProp<ViewStyle>'.\n      Type '(false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined)[]' is not assignable to type 'RecursiveArray<ViewStyle | Falsy | RegisteredStyle<ViewStyle>>'.\n        The types returned by 'pop()' are incompatible between these types.\n          Type 'false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined' is not assignable to type 'ViewStyle | Falsy | RegisteredStyle<ViewStyle> | RecursiveArray<ViewStyle | Falsy | RegisteredStyle<ViewStyle>> | readonly (ViewStyle | ... 1 more ... | RegisteredStyle<...>)[]'.\n            Type '\"\"' is not assignable to type 'ViewStyle | Falsy | RegisteredStyle<ViewStyle> | RecursiveArray<ViewStyle | Falsy | RegisteredStyle<ViewStyle>> | readonly (ViewStyle | ... 1 more ... | RegisteredStyle<...>)[]'.\n  Overload 2 of 2, '(props: ViewProps, context: any): View', gave the following error.\n    Type '(false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined)[]' is not assignable to type 'StyleProp<ViewStyle>'.\n      Type '(false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined)[]' is not assignable to type 'RecursiveArray<ViewStyle | Falsy | RegisteredStyle<ViewStyle>>'.\n        The types returned by 'pop()' are incompatible between these types.\n          Type 'false | \"\" | ViewStyle | { position: \"relative\"; overflow: \"hidden\"; } | { backgroundColor: string; borderWidth: number; } | { backgroundColor: any; borderWidth: number; borderColor: any; } | ... 4 more ... | undefined' is not assignable to type 'ViewStyle | Falsy | RegisteredStyle<ViewStyle> | RecursiveArray<ViewStyle | Falsy | RegisteredStyle<ViewStyle>> | readonly (ViewStyle | ... 1 more ... | RegisteredStyle<...>)[]'.\n            Type '\"\"' is not assignable to type 'ViewStyle | Falsy | RegisteredStyle<ViewStyle> | RecursiveArray<ViewStyle | Falsy | RegisteredStyle<ViewStyle>> | readonly (ViewStyle | ... 1 more ... | RegisteredStyle<...>)[]'.", "source": "ts", "startLineNumber": 198, "startColumn": 15, "endLineNumber": 198, "endColumn": 20, "relatedInformation": [{"startLineNumber": 234, "startColumn": 3, "endLineNumber": 234, "endColumn": 8, "message": "The expected type comes from property 'style' which is declared here on type 'IntrinsicAttributes & IntrinsicClassAttributes<View> & Readonly<ViewProps>'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/node_modules/react-native/Libraries/Components/View/ViewPropTypes.d.ts"}, {"startLineNumber": 234, "startColumn": 3, "endLineNumber": 234, "endColumn": 8, "message": "The expected type comes from property 'style' which is declared here on type 'IntrinsicAttributes & IntrinsicClassAttributes<View> & Readonly<ViewProps>'", "resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/node_modules/react-native/Libraries/Components/View/ViewPropTypes.d.ts"}], "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernInput.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glass' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 291, "startColumn": 29, "endLineNumber": 291, "endColumn": 34, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernInput.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glassBorder' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 293, "startColumn": 25, "endLineNumber": 293, "endColumn": 36, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernLoading.tsx", "owner": "typescript", "code": "2769", "severity": 8, "message": "No overload matches this call.\n  Overload 1 of 2, '(props: AnimateProps<ViewProps>): View', gave the following error.\n    Type '{ width: string; height: number; backgroundColor: string; }' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n      Types of property 'width' are incompatible.\n        Type 'string' is not assignable to type 'DimensionValue | SharedValue<AnimatableValue> | undefined'.\n  Overload 2 of 2, '(props: AnimateProps<ViewProps>, context: any): View', gave the following error.\n    Type '{ width: string; height: number; backgroundColor: string; }' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n      Types of property 'width' are incompatible.\n        Type 'string' is not assignable to type 'DimensionValue | SharedValue<AnimatableValue> | undefined'.", "source": "ts", "startLineNumber": 288, "startColumn": 9, "endLineNumber": 292, "endColumn": 10, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernModal.tsx", "owner": "typescript", "code": "2769", "severity": 8, "message": "No overload matches this call.\n  Overload 1 of 2, '(props: AnimateProps<ViewProps>): View', gave the following error.\n    Type '(ViewStyle | { backgroundColor: string; } | { position: \"absolute\"; bottom: number; left: number; right: number; borderTopLeftRadius: number; borderTopRightRadius: number; borderBottomLeftRadius: number; borderBottomRightRadius: number; } | ... 4 more ... | undefined)[]' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n      Type '(ViewStyle | { backgroundColor: string; } | { position: \"absolute\"; bottom: number; left: number; right: number; borderTopLeftRadius: number; borderTopRightRadius: number; borderBottomLeftRadius: number; borderBottomRightRadius: number; } | ... 4 more ... | undefined)[]' is not assignable to type 'RecursiveArray<false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | RegisteredStyle<...> | null | undefined>'.\n        The types returned by 'pop()' are incompatible between these types.\n          Type 'ViewStyle | { backgroundColor: string; } | { position: \"absolute\"; bottom: number; left: number; right: number; borderTopLeftRadius: number; borderTopRightRadius: number; borderBottomLeftRadius: number; borderBottomRightRadius: number; } | ... 4 more ... | undefined' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n            Type '{ width: string; maxHeight: number; }' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n              Type '{ width: string; maxHeight: number; }' is not assignable to type 'AnimateStyle<ViewStyle>'.\n                Types of property 'width' are incompatible.\n                  Type 'string' is not assignable to type 'DimensionValue | SharedValue<AnimatableValue> | undefined'.\n  Overload 2 of 2, '(props: AnimateProps<ViewProps>, context: any): View', gave the following error.\n    Type '(ViewStyle | { backgroundColor: string; } | { position: \"absolute\"; bottom: number; left: number; right: number; borderTopLeftRadius: number; borderTopRightRadius: number; borderBottomLeftRadius: number; borderBottomRightRadius: number; } | ... 4 more ... | undefined)[]' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n      Type '(ViewStyle | { backgroundColor: string; } | { position: \"absolute\"; bottom: number; left: number; right: number; borderTopLeftRadius: number; borderTopRightRadius: number; borderBottomLeftRadius: number; borderBottomRightRadius: number; } | ... 4 more ... | undefined)[]' is not assignable to type 'RecursiveArray<false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | RegisteredStyle<...> | null | undefined>'.\n        The types returned by 'pop()' are incompatible between these types.\n          Type 'ViewStyle | { backgroundColor: string; } | { position: \"absolute\"; bottom: number; left: number; right: number; borderTopLeftRadius: number; borderTopRightRadius: number; borderBottomLeftRadius: number; borderBottomRightRadius: number; } | ... 4 more ... | undefined' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n            Type '{ width: string; maxHeight: number; }' is not assignable to type 'false | AnimateStyle<ViewStyle> | AnimateStyle<RegisteredStyle<ViewStyle>> | AnimateStyle<RecursiveArray<ViewStyle | Falsy | RegisteredStyle<...>>> | ... 4 more ... | undefined'.\n              Type '{ width: string; maxHeight: number; }' is not assignable to type 'AnimateStyle<ViewStyle>'.\n                Types of property 'width' are incompatible.\n                  Type 'string' is not assignable to type 'DimensionValue | SharedValue<AnimatableValue> | undefined'.", "source": "ts", "startLineNumber": 187, "startColumn": 21, "endLineNumber": 187, "endColumn": 40, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernModal.tsx", "owner": "typescript", "code": "7053", "severity": 8, "message": "Element implicitly has an 'any' type because expression of type '\"glassTitle\" | \"defaultTitle\" | \"centerTitle\" | \"bottomTitle\" | \"fullscreenTitle\"' can't be used to index type '{ overlay: { flex: number; justifyContent: \"center\"; alignItems: \"center\"; }; backdrop: { position: \"absolute\"; top: number; left: number; right: number; bottom: number; backgroundColor: string; }; ... 21 more ...; content: { ...; }; }'.\n  Property 'centerTitle' does not exist on type '{ overlay: { flex: number; justifyContent: \"center\"; alignItems: \"center\"; }; backdrop: { position: \"absolute\"; top: number; left: number; right: number; bottom: number; backgroundColor: string; }; ... 21 more ...; content: { ...; }; }'.", "source": "ts", "startLineNumber": 198, "startColumn": 49, "endLineNumber": 198, "endColumn": 74, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernModal.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glass' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 292, "startColumn": 29, "endLineNumber": 292, "endColumn": 34, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernModal.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glassBorder' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 294, "startColumn": 25, "endLineNumber": 294, "endColumn": 36, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernModal.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glass' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 303, "startColumn": 29, "endLineNumber": 303, "endColumn": 34, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernTabBar.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glass' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 183, "startColumn": 27, "endLineNumber": 183, "endColumn": 32, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/ModernTabBar.tsx", "owner": "typescript", "code": "2339", "severity": 8, "message": "Property 'glassStrong' does not exist on type '{ background: string; backgroundSecondary: string; backgroundTertiary: string; backgroundQuaternary: string; foreground: string; card: string; cardElevated: string; cardForeground: string; ... 123 more ...; primaryOpacity10: string; }'.", "source": "ts", "startLineNumber": 183, "startColumn": 41, "endLineNumber": 183, "endColumn": 52, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/PermissionsScreen.tsx", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type 'string[]' is not assignable to parameter of type 'Permission[]'.\n  Type 'string' is not assignable to type 'Permission'.", "source": "ts", "startLineNumber": 144, "startColumn": 80, "endLineNumber": 144, "endColumn": 97, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/PermissionsScreen.tsx", "owner": "typescript", "code": "2345", "severity": 8, "message": "Argument of type 'string[]' is not assignable to parameter of type 'Permission[]'.\n  Type 'string' is not assignable to type 'Permission'.", "source": "ts", "startLineNumber": 372, "startColumn": 80, "endLineNumber": 372, "endColumn": 97, "origin": "extHost1"}, {"resource": "/c:/Users/<USER>/Downloads/nutri-ai-app/src/components/SplashScreen.tsx", "owner": "typescript", "code": "2322", "severity": 8, "message": "Type 'number' is not assignable to type 'undefined'.", "source": "ts", "startLineNumber": 32, "startColumn": 7, "endLineNumber": 32, "endColumn": 15, "origin": "extHost1"}]