import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  ViewStyle,
  TextStyle,
  StatusBar,
  Pressable,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolate,
  SlideInDown,
  SlideOutDown,
  FadeIn,
  FadeOut,
  ZoomIn,
  ZoomOut,
} from 'react-native-reanimated';
import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ModernModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  variant?: 'default' | 'fullscreen' | 'bottom' | 'center' | 'glass';
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'auto';
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  animationType?: 'slide' | 'fade' | 'zoom' | 'spring';
  backdropOpacity?: number;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  titleStyle?: TextStyle;
  borderRadius?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  hasHeader?: boolean;
  headerActions?: React.ReactNode;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export const ModernModal: React.FC<ModernModalProps> = ({
  visible,
  onClose,
  title,
  children,
  variant = 'default',
  size = 'md',
  showCloseButton = true,
  closeOnBackdrop = true,
  animationType = 'slide',
  backdropOpacity = 0.5,
  style,
  contentStyle,
  titleStyle,
  borderRadius = 'xl',
  hasHeader = true,
  headerActions,
}) => {
  const backdropOpacityValue = useSharedValue(0);
  const scale = useSharedValue(0.9);
  const translateY = useSharedValue(50);

  useEffect(() => {
    if (visible) {
      backdropOpacityValue.value = withTiming(backdropOpacity, { duration: 300 });
      scale.value = withSpring(1, { damping: 20, stiffness: 300 });
      translateY.value = withSpring(0, { damping: 20, stiffness: 300 });
    } else {
      backdropOpacityValue.value = withTiming(0, { duration: 200 });
      scale.value = withTiming(0.9, { duration: 200 });
      translateY.value = withTiming(50, { duration: 200 });
    }
  }, [visible]);

  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacityValue.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value },
    ],
  }));

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose();
    }
  };

  const getModalSize = () => {
    switch (size) {
      case 'sm':
        return { width: screenWidth * 0.8, maxHeight: screenHeight * 0.4 };
      case 'md':
        return { width: screenWidth * 0.9, maxHeight: screenHeight * 0.6 };
      case 'lg':
        return { width: screenWidth * 0.95, maxHeight: screenHeight * 0.8 };
      case 'xl':
        return { width: screenWidth * 0.98, maxHeight: screenHeight * 0.9 };
      case 'auto':
        return { width: 'auto', maxHeight: screenHeight * 0.8 };
      default:
        return { width: screenWidth * 0.9, maxHeight: screenHeight * 0.6 };
    }
  };

  const getContainerStyle = () => {
    const baseStyle = [
      styles.container,
      styles[`${variant}Container`],
      styles[`${borderRadius}BorderRadius`],
      getModalSize(),
      contentStyle,
    ];

    if (variant === 'fullscreen') {
      return [styles.container, styles.fullscreenContainer, contentStyle];
    }

    return baseStyle;
  };

  const getAnimationProps = () => {
    switch (animationType) {
      case 'fade':
        return {
          entering: FadeIn.duration(300),
          exiting: FadeOut.duration(200),
        };
      case 'zoom':
        return {
          entering: ZoomIn.duration(300),
          exiting: ZoomOut.duration(200),
        };
      case 'spring':
        return {
          entering: SlideInDown.springify().damping(20).stiffness(300),
          exiting: SlideOutDown.duration(200),
        };
      default:
        return {
          entering: SlideInDown.duration(300),
          exiting: SlideOutDown.duration(200),
        };
    }
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <StatusBar backgroundColor="rgba(0,0,0,0.5)" barStyle="light-content" />
        
        {/* Backdrop */}
        <AnimatedPressable
          style={[styles.backdrop, backdropAnimatedStyle]}
          onPress={handleBackdropPress}
        />

        {/* Modal Content */}
        <View style={styles.modalWrapper}>
          <Animated.View
            style={[getContainerStyle() as any, contentAnimatedStyle]}
            {...getAnimationProps()}
          >
            {variant === 'glass' && (
              <View style={styles.glassBackground} />
            )}

            {hasHeader && (title || showCloseButton || headerActions) && (
              <View style={styles.header}>
                <View style={styles.headerContent}>
                  {title && (
                    <Text style={[styles.title, (styles as any)[`${variant}Title`], titleStyle]}>
                      {title}
                    </Text>
                  )}
                  
                  <View style={styles.headerActions}>
                    {headerActions}
                    {showCloseButton && (
                      <TouchableOpacity
                        style={styles.closeButton}
                        onPress={onClose}
                        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                      >
                        <Ionicons
                          name="close"
                          size={24}
                          color={variant === 'glass' ? Colors.foreground : Colors.mutedForeground}
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
                
                <View style={styles.headerDivider} />
              </View>
            )}

            {/* Content */}
            <View style={styles.content}>
              {children}
            </View>
          </Animated.View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  
  modalWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  
  // Container Variants
  container: {
    backgroundColor: Colors.background,
    ...Shadows.lg,
    overflow: 'hidden',
  },
  
  defaultContainer: {
    backgroundColor: Colors.background,
  },
  
  fullscreenContainer: {
    width: screenWidth,
    height: screenHeight,
    borderRadius: 0,
  },
  
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: BorderRadius.xxl,
    borderTopRightRadius: BorderRadius.xxl,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  
  centerContainer: {
    backgroundColor: Colors.background,
  },
  
  glassContainer: {
    backgroundColor: Colors.glass,
    borderWidth: 1,
    borderColor: Colors.glassBorder,
  },
  
  glassBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.glass,
  },
  
  // Border Radius Variants
  smBorderRadius: { borderRadius: BorderRadius.sm },
  mdBorderRadius: { borderRadius: BorderRadius.md },
  lgBorderRadius: { borderRadius: BorderRadius.lg },
  xlBorderRadius: { borderRadius: BorderRadius.xl },
  xxlBorderRadius: { borderRadius: BorderRadius.xxl },
  
  // Header
  header: {
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
  },
  
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  headerDivider: {
    height: 1,
    backgroundColor: Colors.border,
    marginTop: Spacing.lg,
  },
  
  // Title
  title: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    flex: 1,
  },
  
  defaultTitle: {
    color: Colors.foreground,
  },
  
  glassTitle: {
    color: Colors.foreground,
  },
  
  // Close Button
  closeButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.muted,
    marginLeft: Spacing.md,
  },
  
  // Content
  content: {
    flex: 1,
    padding: Spacing.xl,
  },
});
