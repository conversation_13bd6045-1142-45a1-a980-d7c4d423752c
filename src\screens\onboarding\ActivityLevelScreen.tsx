import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import MultiSelectCard from '../../components/onboarding/MultiSelectCard';

type ActivityLevelScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'ActivityLevel'>;

const ActivityLevelScreen: React.FC = () => {
  const navigation = useNavigation<ActivityLevelScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [selectedActivity, setSelectedActivity] = useState<string[]>(data.activityLevel ? [data.activityLevel] : []);

  const activityOptions = [
    {
      id: 'sedentary',
      label: 'Sedentary',
      description: 'Little to no exercise, desk job',
      icon: 'laptop' as const,
      color: '#6B7280',
    },
    {
      id: 'lightly_active',
      label: 'Lightly Active',
      description: 'Light exercise 1-3 days/week',
      icon: 'walk' as const,
      color: '#10B981',
    },
    {
      id: 'moderately_active',
      label: 'Moderately Active',
      description: 'Moderate exercise 3-5 days/week',
      icon: 'bicycle' as const,
      color: '#F59E0B',
    },
    {
      id: 'very_active',
      label: 'Very Active',
      description: 'Hard exercise 6-7 days/week',
      icon: 'fitness' as const,
      color: '#EF4444',
    },
  ];

  const handleNext = () => {
    updateData('activityLevel', selectedActivity[0] || 'moderately_active');
    nextStep();
    navigation.navigate('NutritionGoals');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  const isFormValid = () => {
    return selectedActivity.length > 0;
  };

  return (
    <OnboardingLayout
      title="Activity Level"
      subtitle="How active are you on a typical week?"
      onNext={handleNext}
      onBack={handleBack}
      nextDisabled={!isFormValid()}
    >
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <MultiSelectCard
          title="Select your activity level"
          options={activityOptions}
          selectedValues={selectedActivity}
          onSelectionChange={setSelectedActivity}
          allowMultiple={false}
        />
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    paddingTop: 16,
  },
});

export default ActivityLevelScreen;
