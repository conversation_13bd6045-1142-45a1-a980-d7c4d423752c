import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from './ApiService';
import DatabaseIntegrationService from './DatabaseIntegrationService';
import RecipeCacheService from './RecipeCacheService';

// Permanent image cache interface for storing Unsplash images (NEVER expires)
interface PermanentImageCache {
  [mealName: string]: {
    imageUrl: string;
    cachedAt: number;
    // No expiration - images are cached permanently
  };
}

export interface MealData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  imageUrl: string;
  description?: string;
}

export interface WeekPlan {
  week: Array<{
    day: string;
    meals: { [key: string]: MealData };
  }>;
  weekNumber: number;
  year: number;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  isActive: boolean;
  generatedAt: number; // timestamp
}

export interface WeeklyPlanConfig {
  autoGenerate: boolean;
  generateDaysAhead: number; // How many days before week ends to generate next week
  maxStoredWeeks: number; // Maximum number of weeks to keep in storage
}

class WeeklyPlanManager {
  private static instance: WeeklyPlanManager;
  private readonly WEEKLY_PLANS_KEY = 'weekly_plans_storage';
  private readonly CURRENT_WEEK_KEY = 'current_week_info';
  private readonly CONFIG_KEY = 'weekly_plan_config';
  private readonly IMAGE_CACHE_KEY = 'permanent_meal_image_cache';
  // No expiry - images are cached permanently to avoid repeated API calls

  static getInstance(): WeeklyPlanManager {
    if (!WeeklyPlanManager.instance) {
      WeeklyPlanManager.instance = new WeeklyPlanManager();
    }
    return WeeklyPlanManager.instance;
  }

  // Get current week information using proper ISO week calculation
  getCurrentWeekInfo(): { weekNumber: number; year: number; startDate: Date; endDate: Date } {
    const now = new Date();

    // Calculate start of current week (Monday) - ISO week starts on Monday
    const dayOfWeek = now.getDay();
    const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const startDate = new Date(now);
    startDate.setDate(now.getDate() + daysToMonday);
    startDate.setHours(0, 0, 0, 0);

    // Calculate end of current week (Sunday)
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);
    endDate.setHours(23, 59, 59, 999);

    // Proper ISO week number calculation
    const weekNumber = this.getISOWeekNumber(startDate);
    const weekYear = this.getISOWeekYear(startDate);

    console.log(`📅 Week calculation: Week ${weekNumber} of ${weekYear}`);
    console.log(`📅 Week period: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);

    return {
      weekNumber,
      year: weekYear,
      startDate,
      endDate
    };
  }

  // Calculate ISO week number (1-53) - handles year boundaries correctly
  private getISOWeekNumber(date: Date): number {
    const tempDate = new Date(date.getTime());
    const dayOfWeek = (tempDate.getDay() + 6) % 7; // Monday = 0, Sunday = 6
    tempDate.setDate(tempDate.getDate() - dayOfWeek + 3); // Thursday of this week
    const firstThursday = tempDate.getTime();
    tempDate.setMonth(0, 1); // January 1st
    if (tempDate.getDay() !== 4) {
      tempDate.setMonth(0, 1 + ((4 - tempDate.getDay()) + 7) % 7);
    }
    return 1 + Math.ceil((firstThursday - tempDate.getTime()) / 604800000); // 604800000 = 7 * 24 * 3600 * 1000
  }

  // Get the year for ISO week (can differ from calendar year)
  private getISOWeekYear(date: Date): number {
    const tempDate = new Date(date.getTime());
    const dayOfWeek = (tempDate.getDay() + 6) % 7;
    tempDate.setDate(tempDate.getDate() - dayOfWeek + 3); // Thursday of this week
    return tempDate.getFullYear();
  }

  // Enhance meal plan with FULL AI-GENERATED data AND preload ALL images
  private async enhanceMealPlanWithData(basicWeek: Array<{day: string; meals: {[key: string]: string}}>): Promise<Array<{day: string; meals: {[key: string]: MealData}}>> {
    console.log('🤖 Enhancing meal plan with FULL AI-GENERATED data (no static content)...');
    const startTime = Date.now();

    const enhancedWeek = [];
    const allMealNames: string[] = [];

    // First pass: Generate meal data and collect meal names
    for (const day of basicWeek) {
      console.log(`📅 Processing ${day.day} with AI-generated content...`);

      const enhancedDay = {
        day: day.day,
        meals: {} as {[key: string]: MealData}
      };

      // Collect all meal names for image preloading
      Object.values(day.meals).forEach(mealName => {
        if (!allMealNames.includes(mealName)) {
          allMealNames.push(mealName);
        }
      });

      // Process meals with FULL AI-GENERATED data - generate complete recipes
      for (const [mealType, mealName] of Object.entries(day.meals)) {
        try {
          console.log(`🤖 Generating AI content for: ${mealName} (${mealType})`);
          // Generate COMPLETE meal data with full AI recipe generation
          enhancedDay.meals[mealType] = await this.generateCompleteMealData(mealName, mealType);
        } catch (error) {
          console.error(`❌ Error generating AI content for ${mealName}:`, error);
          // Even fallback should be sophisticated, not static
          enhancedDay.meals[mealType] = await this.generateSophisticatedFallback(mealName, mealType);
        }
      }

      enhancedWeek.push(enhancedDay);
    }

    // Second pass: Preload ALL images in parallel BEFORE completing
    console.log(`🖼️ Preloading images for ${allMealNames.length} unique meals...`);
    await this.preloadAllMealImages(allMealNames);

    const endTime = Date.now();
    console.log(`✅ Enhanced meal plan with FULL AI content and preloaded images in ${endTime - startTime}ms`);
    return enhancedWeek;
  }

  // Generate sophisticated fallback using RecipeCacheService (still AI-powered!)
  private async generateSophisticatedFallback(mealName: string, mealType: string): Promise<MealData> {
    try {
      console.log(`🔄 Generating sophisticated fallback for: ${mealName}`);

      // Use RecipeCacheService which has sophisticated fallbacks
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      return {
        name: mealName,
        calories: cachedRecipe.nutrition.calories,
        protein: cachedRecipe.nutrition.protein,
        carbs: cachedRecipe.nutrition.carbs,
        fat: cachedRecipe.nutrition.fat,
        imageUrl: cachedRecipe.imageUrl,
        description: cachedRecipe.description
      };
    } catch (error) {
      console.error(`❌ Even sophisticated fallback failed for ${mealName}:`, error);
      // Last resort - but still better than basic static data
      return await this.getFallbackMealData(mealName, mealType);
    }
  }

  // Basic fallback meal data (last resort only)
  private async getFallbackMealData(mealName: string, mealType: string): Promise<MealData> {
    const imageUrl = await this.getFallbackImage(mealName);

    const calories = this.estimateCalories(mealType);
    return {
      name: mealName,
      calories: calories,
      protein: Math.round((calories * 0.25) / 4), // 25% of calories from protein, 4 cal/g
      carbs: Math.round((calories * 0.45) / 4),   // 45% of calories from carbs, 4 cal/g
      fat: Math.round((calories * 0.30) / 9),     // 30% of calories from fat, 9 cal/g
      imageUrl: imageUrl,
      description: `Healthy ${mealName.toLowerCase()}`
    };
  }

  // Generate FULL AI meal data (ALWAYS generate real content!)
  private async generateBasicMealData(mealName: string, mealType: string): Promise<MealData> {
    console.log(`🤖 Generating FULL AI data for: ${mealName} (${mealType}) - REAL AI generation`);

    try {
      // ALWAYS generate or get AI content - no static data!
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      return {
        name: mealName,
        calories: cachedRecipe.nutrition.calories,
        protein: cachedRecipe.nutrition.protein,
        carbs: cachedRecipe.nutrition.carbs,
        fat: cachedRecipe.nutrition.fat,
        imageUrl: cachedRecipe.imageUrl,
        description: cachedRecipe.description
      };
    } catch (error) {
      console.warn(`⚠️ Error generating AI data for ${mealName}:`, error);
      // Even fallback should try to be AI-powered
      return this.generateSophisticatedFallback(mealName, mealType);
    }
  }

  // Generate complete meal data for a single meal using RecipeCacheService (ONLY used for on-demand generation)
  private async generateCompleteMealData(mealName: string, mealType: string): Promise<MealData> {
    try {
      console.log(`🍽️ Generating COMPLETE data for: ${mealName} (${mealType}) - FULL recipe generation`);

      // Use RecipeCacheService to get or generate recipe
      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(mealName, 'weekly_plan');

      return {
        name: mealName,
        calories: cachedRecipe.nutrition.calories,
        protein: cachedRecipe.nutrition.protein,
        carbs: cachedRecipe.nutrition.carbs,
        fat: cachedRecipe.nutrition.fat,
        imageUrl: cachedRecipe.imageUrl,
        description: cachedRecipe.description
      };
    } catch (error) {
      console.warn(`⚠️ Failed to generate complete data for ${mealName}, using estimates:`, error);
      return await this.getEstimatedMealData(mealName, mealType);
    }
  }

  // Get estimated meal data with dynamic Unsplash image
  private async getEstimatedMealData(mealName: string, mealType: string): Promise<MealData> {
    const calories = this.estimateCalories(mealType);
    const imageUrl = await this.getFallbackImage(mealName);

    return {
      name: mealName,
      calories: calories,
      protein: Math.round((calories * 0.25) / 4), // 25% of calories from protein, 4 cal/g
      carbs: Math.round((calories * 0.45) / 4),   // 45% of calories from carbs, 4 cal/g
      fat: Math.round((calories * 0.30) / 9),     // 30% of calories from fat, 9 cal/g
      imageUrl: imageUrl,
      description: `Healthy ${mealName.toLowerCase()}`
    };
  }

  // Estimate calories based on meal type
  private estimateCalories(mealType: string): number {
    const calorieMap: {[key: string]: number} = {
      breakfast: 400,
      lunch: 500,
      dinner: 600,
      snack: 200,
      snack1: 150,
      snack2: 150
    };
    return calorieMap[mealType.toLowerCase()] || 400;
  }

  // Get cached image or fetch new one from Unsplash (PERMANENT CACHING - never expires)
  private async getFallbackImage(mealName: string): Promise<string> {
    // First check if we have a permanently cached image for this meal
    const cachedImage = await this.getCachedImage(mealName);
    if (cachedImage) {
      console.log(`📖 Using PERMANENTLY cached image for ${mealName} - NO API CALL NEEDED: ${cachedImage}`);
      return cachedImage;
    }

    // If not cached, fetch from Unsplash and cache it
    const searchTerm = mealName.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
    // Enhance search term by adding "food" for better food-related results
    const enhancedSearchTerm = `${searchTerm} food`;
    const accessKey = 'QPySZeLMRd2Rw0BKoNKpXFwrHY0aSVZMwxvTmZaIZEs';
    const unsplashApiUrl = 'https://api.unsplash.com';

    try {
      console.log(`🖼️ WeeklyPlanManager: PARALLEL Unsplash API call for: ${mealName} (enhanced: ${enhancedSearchTerm}) (will be cached PERMANENTLY)`);

      // Create PARALLEL requests for maximum speed
      const specificSearchPromise = fetch(`${unsplashApiUrl}/search/photos?query=${encodeURIComponent(enhancedSearchTerm)}&orientation=landscape&order_by=relevant&per_page=1&client_id=${accessKey}`);
      const genericSearchPromise = fetch(`${unsplashApiUrl}/search/photos?query=food&orientation=landscape&order_by=relevant&per_page=5&client_id=${accessKey}`);

      // Execute BOTH requests in parallel with 2-second timeout
      const [specificResult, genericResult] = await Promise.allSettled([
        Promise.race([specificSearchPromise, new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 2000))]),
        Promise.race([genericSearchPromise, new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 2000))])
      ]);

      // Try specific search first (fastest response)
      if (specificResult.status === 'fulfilled' && (specificResult.value as Response).ok) {
        const data = await (specificResult.value as Response).json();
        if (data.results && data.results.length > 0) {
          const imageUrl = data.results[0].urls.small;
          console.log(`✅ WeeklyPlanManager: FAST specific image found for ${enhancedSearchTerm}: ${imageUrl}`);

          // Cache the image for future use
          await this.cacheImage(mealName, imageUrl);
          return imageUrl;
        }
      }

      // Fallback to generic food search (parallel backup)
      if (genericResult.status === 'fulfilled' && (genericResult.value as Response).ok) {
        console.log(`⚠️ WeeklyPlanManager: Using PARALLEL generic food search for ${mealName}`);
        const genericData = await (genericResult.value as Response).json();
        if (genericData.results && genericData.results.length > 0) {
          const imageUrl = genericData.results[0].urls.small;
          console.log(`✅ WeeklyPlanManager: FAST generic food image for ${mealName}: ${imageUrl}`);

          // Cache the generic image
          await this.cacheImage(mealName, imageUrl);
          return imageUrl;
        }
      }

      // Both parallel requests failed
      console.log(`⚠️ WeeklyPlanManager: Both parallel requests failed for ${mealName}, using static fallback`);
      throw new Error('Both parallel requests failed');

    } catch (error) {
      console.warn(`⚠️ WeeklyPlanManager: Failed to fetch Unsplash image for ${enhancedSearchTerm} (original: ${mealName}):`, error);

      // Return optimized static fallback image (400px width for better performance)
      const fallbackImage = `https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop&crop=center&q=80&auto=format&cs=tinysrgb&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D`;

      // Cache the fallback image too
      await this.cacheImage(mealName, fallbackImage);
      return fallbackImage;
    }
  }

  // Get permanently cached image for a meal (NEVER expires)
  private async getCachedImage(mealName: string): Promise<string | null> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return null;

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const normalizedMealName = mealName.toLowerCase().trim();
      const cachedEntry = cache[normalizedMealName];

      if (!cachedEntry) return null;

      // Images NEVER expire - return cached image immediately
      console.log(`📖 Found permanently cached image for ${mealName} (cached ${new Date(cachedEntry.cachedAt).toLocaleDateString()})`);
      return cachedEntry.imageUrl;
    } catch (error) {
      console.error('❌ Error getting cached image:', error);
      return null;
    }
  }

  // Cache an image for a meal PERMANENTLY (NEVER expires)
  private async cacheImage(mealName: string, imageUrl: string): Promise<void> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      const cache: PermanentImageCache = cacheJson ? JSON.parse(cacheJson) : {};

      const normalizedMealName = mealName.toLowerCase().trim();
      const now = Date.now();

      // Only cache if not already cached (avoid overwriting existing entries)
      if (!cache[normalizedMealName]) {
        cache[normalizedMealName] = {
          imageUrl,
          cachedAt: now
          // No expiration - cached permanently
        };

        await AsyncStorage.setItem(this.IMAGE_CACHE_KEY, JSON.stringify(cache));
        console.log(`💾 PERMANENTLY cached image for ${mealName} - will NEVER make API call again for this meal`);
      } else {
        console.log(`📖 Image already permanently cached for ${mealName} - skipping cache update`);
      }
    } catch (error) {
      console.error('❌ Error caching image:', error);
    }
  }

  // Preload ALL meal images in parallel for instant display
  private async preloadAllMealImages(mealNames: string[]): Promise<void> {
    const startTime = Date.now();
    console.log(`🚀 Starting parallel image preloading for ${mealNames.length} meals...`);

    try {
      // Create promises for all image fetches
      const imagePromises = mealNames.map(async (mealName, index) => {
        try {
          console.log(`🖼️ [${index + 1}/${mealNames.length}] Preloading image for: ${mealName}`);

          // Check if already cached first
          const cachedImage = await this.getCachedImage(mealName);
          if (cachedImage) {
            console.log(`📖 [${index + 1}/${mealNames.length}] Image already cached for: ${mealName}`);
            return { mealName, imageUrl: cachedImage, fromCache: true };
          }

          // Fetch new image if not cached
          const imageUrl = await this.getFallbackImage(mealName);
          console.log(`✅ [${index + 1}/${mealNames.length}] Preloaded image for: ${mealName}`);
          return { mealName, imageUrl, fromCache: false };
        } catch (error) {
          console.error(`❌ [${index + 1}/${mealNames.length}] Failed to preload image for ${mealName}:`, error);
          return { mealName, imageUrl: null, fromCache: false, error: true };
        }
      });

      // Wait for all images to be fetched/cached
      const results = await Promise.all(imagePromises);

      // Log results
      const cached = results.filter(r => r.fromCache).length;
      const fetched = results.filter(r => !r.fromCache && !r.error).length;
      const failed = results.filter(r => r.error).length;

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      console.log(`🎉 Image preloading completed in ${totalTime}ms:`);
      console.log(`   📖 Cached: ${cached} images`);
      console.log(`   🆕 Fetched: ${fetched} images`);
      console.log(`   ❌ Failed: ${failed} images`);
      console.log(`   ⚡ Average: ${(totalTime / mealNames.length).toFixed(1)}ms per image`);

      if (failed > 0) {
        console.warn(`⚠️ ${failed} images failed to preload, but meal plan will still work with fallbacks`);
      }

    } catch (error) {
      console.error('❌ Critical error during image preloading:', error);
      // Don't throw - meal plan should still work without images
    }
  }

  // Get cache statistics (for debugging) - no expiration cleanup needed
  async getImageCacheStats(): Promise<{ totalImages: number; cacheSize: string; oldestCacheDate: string }> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return { totalImages: 0, cacheSize: '0 KB', oldestCacheDate: 'N/A' };

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const totalImages = Object.keys(cache).length;
      const cacheSize = `${Math.round(cacheJson.length / 1024)} KB`;

      // Find oldest cached image
      const oldestEntry = Object.values(cache).reduce((oldest, current) =>
        current.cachedAt < oldest.cachedAt ? current : oldest
      );
      const oldestCacheDate = oldestEntry ? new Date(oldestEntry.cachedAt).toLocaleDateString() : 'N/A';

      return { totalImages, cacheSize, oldestCacheDate };
    } catch (error) {
      console.error('❌ Error getting cache stats:', error);
      return { totalImages: 0, cacheSize: '0 KB', oldestCacheDate: 'N/A' };
    }
  }

  // Clear ALL cached images (only if user explicitly wants to reset)
  async clearAllImageCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.IMAGE_CACHE_KEY);
      console.log(`🧹 Cleared ALL permanently cached images - API calls will be made again for all meals`);
    } catch (error) {
      console.error('❌ Error clearing all image cache:', error);
    }
  }

  // Check if an image is already permanently cached (for debugging/optimization)
  async isImageCached(mealName: string): Promise<boolean> {
    try {
      const cacheJson = await AsyncStorage.getItem(this.IMAGE_CACHE_KEY);
      if (!cacheJson) return false;

      const cache: PermanentImageCache = JSON.parse(cacheJson);
      const normalizedMealName = mealName.toLowerCase().trim();
      return !!cache[normalizedMealName];
    } catch (error) {
      console.error('❌ Error checking if image is cached:', error);
      return false;
    }
  }

  // Replace a specific meal in the current week plan (for alternatives)
  async replaceMealInCurrentPlan(dayName: string, mealType: string, userProfile: any): Promise<MealData | null> {
    try {
      console.log(`🔄 Generating alternative meal for ${dayName} ${mealType}`);

      // Get current plan
      const currentPlan = await this.getActiveWeeklyPlan();
      if (!currentPlan) {
        throw new Error('No active plan found');
      }

      // Find the current meal to get context for better alternatives
      const dayIndex = currentPlan.week.findIndex(day => day.day.toLowerCase() === dayName.toLowerCase());
      if (dayIndex === -1) {
        throw new Error(`Day ${dayName} not found in current plan`);
      }

      const currentMeal = currentPlan.week[dayIndex].meals[mealType];
      if (!currentMeal) {
        throw new Error(`Meal type ${mealType} not found for ${dayName}`);
      }

      // Build goal for alternative meal generation
      const goalComponents = [];
      goalComponents.push(`Generate a different ${mealType} meal to replace "${currentMeal.name}"`);

      if (userProfile.dietaryRestrictions?.length > 0) {
        goalComponents.push(`Dietary restrictions: ${userProfile.dietaryRestrictions.join(', ')}`);
      }
      if (userProfile.allergies?.length > 0) {
        goalComponents.push(`Avoid allergens: ${userProfile.allergies.join(', ')}`);
      }
      if (userProfile.preferredCuisines?.length > 0) {
        goalComponents.push(`Preferred cuisines: ${userProfile.preferredCuisines.join(', ')}`);
      }

      const goal = goalComponents.join('. ');

      // Generate new meal name
      const newMealName = await ApiService.generateSingleMeal(goal);

      // Generate complete meal data using RecipeCacheService
      const newMealData = await this.generateCompleteMealData(newMealName, mealType);

      // Update ONLY the specific meal in the current plan
      currentPlan.week[dayIndex].meals[mealType] = newMealData;

      // Use targeted update method instead of full plan storage
      await this.updateSpecificMealInStorage(currentPlan, dayIndex, mealType, newMealData);

      console.log(`✅ Replaced ${dayName} ${mealType}: "${currentMeal.name}" → "${newMealName}"`);
      return newMealData;

    } catch (error) {
      console.error('❌ Error replacing meal:', error);
      return null;
    }
  }

  // Targeted method to update only a specific meal without affecting the rest of the plan
  private async updateSpecificMealInStorage(plan: WeekPlan, dayIndex: number, mealType: string, newMealData: MealData): Promise<void> {
    try {
      // Get existing plans from storage
      const existingPlansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      const existingPlans: WeekPlan[] = existingPlansJson ? JSON.parse(existingPlansJson) : [];

      // Find and update the current active plan
      const activePlanIndex = existingPlans.findIndex(p =>
        p.isActive &&
        p.weekNumber === plan.weekNumber &&
        p.year === plan.year
      );

      if (activePlanIndex !== -1) {
        // Update only the specific meal
        existingPlans[activePlanIndex].week[dayIndex].meals[mealType] = newMealData;

        // Save back to storage
        await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(existingPlans));

        // CRITICAL FIX: Update current week info after meal update
        const currentWeekInfo = this.getCurrentWeekInfo();
        await this.updateCurrentWeekInfo(currentWeekInfo);

        console.log(`✅ Updated specific meal in storage: ${plan.week[dayIndex].day} ${mealType}`);
      } else {
        console.warn('⚠️ Active plan not found in storage, falling back to full plan update');
        await this.saveWeekPlan(plan); // Use saveWeekPlan instead of storeWeeklyPlan
      }

    } catch (error) {
      console.error('❌ Error updating specific meal in storage:', error);
      // Fallback to full plan update
      await this.saveWeekPlan(plan); // Use saveWeekPlan instead of storeWeeklyPlan
    }
  }

  // Check if current stored plan is still valid for this week
  async isCurrentPlanValid(): Promise<boolean> {
    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      const storedWeekInfo = await AsyncStorage.getItem(this.CURRENT_WEEK_KEY);

      if (!storedWeekInfo) {
        console.log('📅 No stored week info found - will regenerate');
        return false;
      }

      let stored;
      try {
        stored = JSON.parse(storedWeekInfo);
      } catch (parseError) {
        console.error('❌ Corrupted week info in storage, clearing:', parseError);
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        return false;
      }

      // Validate stored data structure
      if (!stored || typeof stored.weekNumber !== 'number' || typeof stored.year !== 'number') {
        console.error('❌ Invalid week info structure, clearing');
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        return false;
      }

      const isValid = stored.weekNumber === currentWeekInfo.weekNumber &&
                     stored.year === currentWeekInfo.year;

      console.log(`📅 Week plan validity check: ${isValid ? 'VALID' : 'EXPIRED'}`);
      console.log(`📅 Current: Week ${currentWeekInfo.weekNumber}, ${currentWeekInfo.year}`);
      console.log(`📅 Stored: Week ${stored.weekNumber}, ${stored.year}`);

      // Additional check: verify we actually have an active plan for this week
      if (isValid) {
        const activePlan = await this.getActiveWeeklyPlan();
        if (!activePlan || activePlan.weekNumber !== currentWeekInfo.weekNumber || activePlan.year !== currentWeekInfo.year) {
          console.log('📅 Week info valid but no matching active plan found');
          return false;
        }
      }

      return isValid;
    } catch (error) {
      console.error('❌ Error checking plan validity:', error);
      // Clear potentially corrupted data
      try {
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
      } catch (clearError) {
        console.error('❌ Error clearing corrupted week info:', clearError);
      }
      return false;
    }
  }

  // Generate new weekly plan for current week
  async generateWeeklyPlan(userProfile: any): Promise<WeekPlan | null> {
    const startTime = performance.now();
    console.log('🚀 PERFORMANCE: Starting weekly plan generation...');

    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      console.log(`📅 Generating new weekly plan for Week ${currentWeekInfo.weekNumber}, ${currentWeekInfo.year}`);
      console.log('👤 User profile data:', JSON.stringify({
        dietaryRestrictions: userProfile.dietaryRestrictions,
        caloriesGoal: userProfile.caloriesGoal,
        allergies: userProfile.allergies,
        preferredCuisines: userProfile.preferredCuisines,
        activityLevel: userProfile.activityLevel,
        healthGoals: userProfile.healthGoals
      }, null, 2));

      // Map user profile to API format with proper field names and comprehensive health goals
      const apiOptions = {
        dietaryRestrictions: userProfile.dietaryRestrictions || [],
        calorieGoal: userProfile.caloriesGoal || userProfile.dailyCalorieGoal || 2000, // Handle both field names
        mealsPerDay: 3, // Default to 3 meals per day
        preferences: userProfile.dietaryPreferences || userProfile.foodPreferences || [],
        allergies: userProfile.allergies || [],
        preferredCuisines: userProfile.preferredCuisines || [],
        activityLevel: userProfile.activityLevel || 'Moderate',
        healthGoals: [
          ...(userProfile.healthGoals || []),
          ...(userProfile.fitnessObjectives || []),
          ...(userProfile.healthConditions || []),
          userProfile.weightGoal ? `${userProfile.weightGoal} weight` : ''
        ].filter(Boolean)
      };

      console.log('🔄 Mapped API options with comprehensive health goals:', JSON.stringify(apiOptions, null, 2));

      // Generate meal plan using the corrected API method
      const mealPlanResult = await ApiService.generateWeeklyMealPlan(apiOptions);

      if (!mealPlanResult || !mealPlanResult.week) {
        throw new Error('Failed to generate meal plan from API');
      }

      // Enhance meal plan with complete meal data (nutrition + images)
      const enhancedWeek = await this.enhanceMealPlanWithData(mealPlanResult.week);

      // Validate nutrition data and regenerate if needed
      await this.validateAndFixNutritionData(enhancedWeek);

      const weekPlan: WeekPlan = {
        week: enhancedWeek,
        weekNumber: currentWeekInfo.weekNumber,
        year: currentWeekInfo.year,
        startDate: currentWeekInfo.startDate.toISOString(),
        endDate: currentWeekInfo.endDate.toISOString(),
        isActive: true,
        generatedAt: Date.now()
      };

      // Store the new plan
      await this.storeWeeklyPlan(weekPlan);
      await this.updateCurrentWeekInfo(currentWeekInfo);

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.log(`🚀 PERFORMANCE: Weekly plan generation completed in ${totalTime.toFixed(2)}ms`);
      console.log(`✅ Generated and stored weekly plan for Week ${currentWeekInfo.weekNumber}`);

      // Performance metrics
      const mealsCount = enhancedWeek.reduce((total, day) => total + Object.keys(day.meals).length, 0);
      console.log(`📊 PERFORMANCE METRICS:
        - Total time: ${totalTime.toFixed(2)}ms
        - Meals processed: ${mealsCount}
        - Average time per meal: ${(totalTime / mealsCount).toFixed(2)}ms
        - Performance mode: OPTIMIZED (basic data only)`);

      return weekPlan;

    } catch (error) {
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      console.error(`❌ Error generating weekly plan in ${totalTime.toFixed(2)}ms:`, error);
      console.error('❌ Full error details:', error);
      return null;
    }
  }

  // Store weekly plan in AsyncStorage
  async storeWeeklyPlan(weekPlan: WeekPlan): Promise<void> {
    try {
      // Get existing plans
      const existingPlansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      const existingPlans: WeekPlan[] = existingPlansJson ? JSON.parse(existingPlansJson) : [];

      // Find if this plan already exists (for updates)
      const existingPlanIndex = existingPlans.findIndex(plan =>
        plan.weekNumber === weekPlan.weekNumber &&
        plan.year === weekPlan.year
      );

      if (existingPlanIndex !== -1) {
        // Update existing plan (preserve its active status unless explicitly setting a new active plan)
        existingPlans[existingPlanIndex] = {
          ...existingPlans[existingPlanIndex],
          ...weekPlan,
          // Only change active status if the new plan is explicitly marked as active
          isActive: weekPlan.isActive !== undefined ? weekPlan.isActive : existingPlans[existingPlanIndex].isActive
        };
      } else {
        // New plan - only mark others as inactive if this new plan is active
        if (weekPlan.isActive) {
          existingPlans.forEach(plan => {
            if (plan.isActive) {
              plan.isActive = false;
            }
          });
        }
        existingPlans.push(weekPlan);
      }

      // Keep only recent weeks (cleanup old plans)
      const config = await this.getConfig();
      const sortedPlans = existingPlans.sort((a, b) => b.generatedAt - a.generatedAt);
      const plansToKeep = sortedPlans.slice(0, config.maxStoredWeeks);

      await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(plansToKeep));

      // Also store in database for backup (if available)
      try {
        await DatabaseIntegrationService.saveWeeklyPlanToDatabase(weekPlan);
      } catch (error) {
        console.warn('⚠️ Could not save to database, continuing with AsyncStorage only:', error);
      }

    } catch (error) {
      console.error('❌ Error storing weekly plan:', error);
      throw error;
    }
  }

  // CRITICAL FIX: Add missing saveWeekPlan method that was being called but didn't exist
  async saveWeekPlan(weekPlan: WeekPlan): Promise<void> {
    try {
      console.log('💾 Saving week plan with current week info update...');

      // Store the plan
      await this.storeWeeklyPlan(weekPlan);

      // CRITICAL: Update current week info so isCurrentPlanValid() works correctly
      const currentWeekInfo = this.getCurrentWeekInfo();
      await this.updateCurrentWeekInfo(currentWeekInfo);

      console.log('✅ Week plan saved successfully with current week info updated');
    } catch (error) {
      console.error('❌ Error in saveWeekPlan:', error);
      throw error;
    }
  }

  // Update current week info with retry logic and verification
  private async updateCurrentWeekInfo(weekInfo: any): Promise<void> {
    let attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts) {
      try {
        await AsyncStorage.setItem(this.CURRENT_WEEK_KEY, JSON.stringify(weekInfo));
        console.log(`📅 Updated current week info: Week ${weekInfo.weekNumber}, ${weekInfo.year}`);

        // Verify the save worked
        const verification = await AsyncStorage.getItem(this.CURRENT_WEEK_KEY);
        if (verification) {
          const parsed = JSON.parse(verification);
          if (parsed.weekNumber === weekInfo.weekNumber && parsed.year === weekInfo.year) {
            console.log('✅ Current week info save verified');
            return;
          }
        }

        throw new Error('Week info verification failed');
      } catch (error) {
        attempts++;
        console.error(`❌ Attempt ${attempts}/${maxAttempts} to update week info failed:`, error);

        if (attempts < maxAttempts) {
          console.log(`🔄 Retrying week info update in 500ms...`);
          await new Promise(resolve => setTimeout(resolve, 500));
        } else {
          console.error('❌ Failed to update current week info after all attempts');
          throw error;
        }
      }
    }
  }

  // Get active weekly plan with enhanced error handling and recovery
  async getActiveWeeklyPlan(): Promise<WeekPlan | null> {
    try {
      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) {
        console.log('📅 No plans found in storage');
        return null;
      }

      let plans: WeekPlan[];
      try {
        plans = JSON.parse(plansJson);
      } catch (parseError) {
        console.error('❌ Corrupted plans data in storage, clearing:', parseError);
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        return null;
      }

      // Validate plans array
      if (!Array.isArray(plans)) {
        console.error('❌ Invalid plans data structure, clearing');
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        return null;
      }

      // Find active plans
      const activePlans = plans.filter(plan => plan.isActive);

      if (activePlans.length === 0) {
        console.log('📅 No active plans found');
        return null;
      }

      if (activePlans.length > 1) {
        console.warn('⚠️ Multiple active plans found, using most recent');
        // Fix multiple active plans by keeping only the most recent
        const mostRecent = activePlans.sort((a, b) => b.generatedAt - a.generatedAt)[0];

        // Deactivate others
        plans.forEach(plan => {
          if (plan.isActive && plan !== mostRecent) {
            plan.isActive = false;
          }
        });

        // Save corrected plans
        await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(plans));
        return mostRecent;
      }

      const activePlan = activePlans[0];

      // Validate plan structure
      if (!activePlan.week || !Array.isArray(activePlan.week) ||
          typeof activePlan.weekNumber !== 'number' ||
          typeof activePlan.year !== 'number') {
        console.error('❌ Invalid active plan structure');
        return null;
      }

      console.log(`📅 Found active plan: Week ${activePlan.weekNumber}, ${activePlan.year}`);
      return activePlan;
    } catch (error) {
      console.error('❌ Error getting active weekly plan:', error);
      return null;
    }
  }

  // Get configuration
  private async getConfig(): Promise<WeeklyPlanConfig> {
    try {
      const configJson = await AsyncStorage.getItem(this.CONFIG_KEY);
      if (configJson) {
        return JSON.parse(configJson);
      }
      
      // Default configuration
      const defaultConfig: WeeklyPlanConfig = {
        autoGenerate: true,
        generateDaysAhead: 2, // Generate new plan 2 days before current week ends
        maxStoredWeeks: 8 // Keep 8 weeks of history
      };
      
      await AsyncStorage.setItem(this.CONFIG_KEY, JSON.stringify(defaultConfig));
      return defaultConfig;
    } catch (error) {
      console.error('❌ Error getting config:', error);
      return {
        autoGenerate: true,
        generateDaysAhead: 2,
        maxStoredWeeks: 8
      };
    }
  }

  // Check if we need to generate next week's plan
  async shouldGenerateNextWeek(): Promise<boolean> {
    try {
      const config = await this.getConfig();
      if (!config.autoGenerate) return false;
      
      const currentWeekInfo = this.getCurrentWeekInfo();
      const now = new Date();
      const daysUntilWeekEnd = Math.ceil((currentWeekInfo.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      console.log(`📅 Days until week end: ${daysUntilWeekEnd}`);
      console.log(`📅 Generate days ahead setting: ${config.generateDaysAhead}`);
      
      return daysUntilWeekEnd <= config.generateDaysAhead;
    } catch (error) {
      console.error('❌ Error checking if should generate next week:', error);
      return false;
    }
  }

  // Main function to ensure current week has a valid plan
  async ensureCurrentWeekPlan(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('📅 Ensuring current week has valid plan...');

      if (!userProfile) {
        console.error('❌ No user profile provided, cannot ensure plan');
        return null;
      }

      // Check if current plan is valid
      const isValid = await this.isCurrentPlanValid();

      if (isValid) {
        console.log('📅 Current plan is valid, returning existing plan');
        const existingPlan = await this.getActiveWeeklyPlan();

        if (existingPlan) {
          // Double-check the plan is actually for the current week
          const currentWeekInfo = this.getCurrentWeekInfo();
          if (existingPlan.weekNumber === currentWeekInfo.weekNumber &&
              existingPlan.year === currentWeekInfo.year) {
            return existingPlan;
          } else {
            console.log('📅 Plan week mismatch, regenerating');
          }
        }
      }

      console.log('📅 Current plan is invalid or missing, generating new plan');

      // Try to generate new plan with retry logic
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts) {
        try {
          const newPlan = await this.generateWeeklyPlan(userProfile);
          if (newPlan) {
            console.log('✅ Successfully generated new weekly plan');
            return newPlan;
          }
        } catch (generateError) {
          attempts++;
          console.error(`❌ Attempt ${attempts}/${maxAttempts} to generate plan failed:`, generateError);

          if (attempts < maxAttempts) {
            console.log(`🔄 Retrying plan generation in 1 second...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }

      console.error('❌ Failed to generate plan after all attempts');
      return null;

    } catch (error) {
      console.error('❌ Error ensuring current week plan:', error);
      return null;
    }
  }

  // Get plan history for analytics
  async getPlanHistory(limit: number = 4): Promise<WeekPlan[]> {
    try {
      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) return [];

      const plans: WeekPlan[] = JSON.parse(plansJson);
      return plans
        .sort((a, b) => b.generatedAt - a.generatedAt)
        .slice(0, limit);
    } catch (error) {
      console.error('❌ Error getting plan history:', error);
      return [];
    }
  }

  // Comprehensive cleanup method to handle year transitions and old data with corruption recovery
  async performMaintenanceCleanup(): Promise<void> {
    try {
      console.log('🧹 Performing weekly plan maintenance cleanup...');

      const plansJson = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (!plansJson) {
        console.log('🧹 No plans found for cleanup');
        return;
      }

      let plans: WeekPlan[];
      try {
        plans = JSON.parse(plansJson);
      } catch (parseError) {
        console.error('❌ Corrupted plans data detected during cleanup, clearing:', parseError);
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        return;
      }

      // Validate plans array structure
      if (!Array.isArray(plans)) {
        console.error('❌ Invalid plans data structure, clearing');
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        return;
      }

      const currentWeekInfo = this.getCurrentWeekInfo();
      const currentTime = Date.now();
      const oneYearAgo = currentTime - (365 * 24 * 60 * 60 * 1000);

      // Filter out plans older than 1 year and inactive plans older than 8 weeks, plus validate structure
      const filteredPlans = plans.filter(plan => {
        // Check plan structure first
        if (!plan || typeof plan.generatedAt !== 'number' ||
            typeof plan.weekNumber !== 'number' ||
            typeof plan.year !== 'number' ||
            !Array.isArray(plan.week)) {
          console.warn('🧹 Removing invalid plan structure during cleanup');
          return false;
        }

        const planAge = currentTime - plan.generatedAt;
        const eightWeeksAgo = currentTime - (8 * 7 * 24 * 60 * 60 * 1000);

        // Keep if less than 1 year old
        if (plan.generatedAt > oneYearAgo) {
          // If inactive and older than 8 weeks, remove
          if (!plan.isActive && plan.generatedAt < eightWeeksAgo) {
            return false;
          }
          return true;
        }
        return false;
      });

      // Ensure only one active plan exists
      const activePlans = filteredPlans.filter(plan => plan.isActive);
      if (activePlans.length > 1) {
        console.warn('🧹 Multiple active plans found during cleanup, fixing...');
        const mostRecent = activePlans.sort((a, b) => b.generatedAt - a.generatedAt)[0];
        filteredPlans.forEach(plan => {
          if (plan.isActive && plan !== mostRecent) {
            plan.isActive = false;
          }
        });
        console.log('✅ Fixed multiple active plans issue during cleanup');
      }

      // Ensure current week has an active plan
      const hasCurrentWeekPlan = filteredPlans.some(plan =>
        plan.weekNumber === currentWeekInfo.weekNumber &&
        plan.year === currentWeekInfo.year &&
        plan.isActive
      );

      if (!hasCurrentWeekPlan) {
        console.log('⚠️ No active plan for current week found during cleanup');
      }

      // Save cleaned plans
      await AsyncStorage.setItem(this.WEEKLY_PLANS_KEY, JSON.stringify(filteredPlans));

      console.log(`✅ Cleanup complete: Kept ${filteredPlans.length} plans, removed ${plans.length - filteredPlans.length} old/invalid plans`);

    } catch (error) {
      console.error('❌ Error during maintenance cleanup:', error);
      // If cleanup fails completely, clear everything to prevent corruption
      try {
        await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
        await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);
        console.log('🧹 Cleared all plan data due to cleanup failure');
      } catch (clearError) {
        console.error('❌ Failed to clear corrupted data:', clearError);
      }
    }
  }

  // Initialize weekly plan system - call this on app startup
  async initializeWeeklyPlanSystem(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🚀 Initializing weekly plan system...');

      if (!userProfile) {
        console.error('❌ No user profile provided for initialization');
        return null;
      }

      // Perform maintenance cleanup first with error handling
      try {
        await this.performMaintenanceCleanup();
        console.log('✅ Maintenance cleanup completed');
      } catch (cleanupError) {
        console.error('❌ Maintenance cleanup failed (non-critical):', cleanupError);
        // Continue initialization even if cleanup fails
      }

      // Ensure current week has a valid plan
      const currentPlan = await this.ensureCurrentWeekPlan(userProfile);

      if (!currentPlan) {
        console.error('❌ Failed to ensure current week plan');
        return null;
      }

      // Validate and fix nutrition data for existing plan
      try {
        await this.validateAndFixNutritionData(currentPlan.week);
        // Save the plan if any nutrition data was fixed
        await this.saveWeekPlan(currentPlan);
        console.log('✅ Nutrition data validated and plan saved');
      } catch (nutritionError) {
        console.error('❌ Error validating nutrition data (non-critical):', nutritionError);
        // Continue even if nutrition validation fails
      }

      // Check if we should pre-generate next week
      try {
        const shouldGenerateNext = await this.shouldGenerateNextWeek();
        if (shouldGenerateNext && userProfile) {
          console.log('📅 Pre-generating next week\'s plan in background...');
          // Don't await this - let it run in background
          this.generateNextWeekPlan(userProfile).catch(error => {
            console.error('❌ Error pre-generating next week:', error);
          });
        }
      } catch (nextWeekError) {
        console.error('❌ Error checking next week generation (non-critical):', nextWeekError);
      }

      console.log('✅ Weekly plan system initialized successfully');
      return currentPlan;

    } catch (error) {
      console.error('❌ Critical error initializing weekly plan system:', error);

      // Try to recover by returning any existing plan
      try {
        const fallbackPlan = await this.getActiveWeeklyPlan();
        if (fallbackPlan) {
          console.log('🔄 Returning fallback plan from storage');
          return fallbackPlan;
        }
      } catch (fallbackError) {
        console.error('❌ Fallback plan retrieval failed:', fallbackError);
      }

      return null;
    }
  }

  // Emergency recovery method to ensure app always has a plan
  async emergencyPlanRecovery(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🚨 Emergency plan recovery initiated...');

      // Clear potentially corrupted data
      await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
      await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);

      // Force generate a new plan
      const newPlan = await this.generateWeeklyPlan(userProfile);

      if (newPlan) {
        console.log('✅ Emergency plan recovery successful');
        return newPlan;
      } else {
        console.error('❌ Emergency plan recovery failed');
        return null;
      }
    } catch (error) {
      console.error('❌ Emergency plan recovery error:', error);
      return null;
    }
  }

  // Generate next week's plan in advance
  private async generateNextWeekPlan(userProfile: any): Promise<void> {
    try {
      const currentWeekInfo = this.getCurrentWeekInfo();
      const nextWeekInfo = {
        weekNumber: currentWeekInfo.weekNumber === 53 ? 1 : currentWeekInfo.weekNumber + 1,
        year: currentWeekInfo.weekNumber === 53 ? currentWeekInfo.year + 1 : currentWeekInfo.year,
        startDate: new Date(currentWeekInfo.startDate.getTime() + (7 * 24 * 60 * 60 * 1000)),
        endDate: new Date(currentWeekInfo.endDate.getTime() + (7 * 24 * 60 * 60 * 1000))
      };

      // Check if next week already has a plan
      const existingPlans = await AsyncStorage.getItem(this.WEEKLY_PLANS_KEY);
      if (existingPlans) {
        const plans: WeekPlan[] = JSON.parse(existingPlans);
        const hasNextWeekPlan = plans.some(plan =>
          plan.weekNumber === nextWeekInfo.weekNumber &&
          plan.year === nextWeekInfo.year
        );

        if (hasNextWeekPlan) {
          console.log('📅 Next week already has a plan, skipping generation');
          return;
        }
      }

      console.log(`📅 Generating plan for next week: Week ${nextWeekInfo.weekNumber}, ${nextWeekInfo.year}`);

      // Generate the plan for next week
      const apiOptions = {
        dietaryRestrictions: userProfile.dietaryRestrictions || [],
        calorieGoal: userProfile.caloriesGoal || userProfile.dailyCalorieGoal || 2000,
        mealsPerDay: 3,
        preferences: userProfile.dietaryPreferences || userProfile.foodPreferences || [],
        allergies: userProfile.allergies || [],
        preferredCuisines: userProfile.preferredCuisines || [],
        activityLevel: userProfile.activityLevel || 'Moderate',
        healthGoals: [
          ...(userProfile.healthGoals || []),
          ...(userProfile.fitnessObjectives || []),
          ...(userProfile.healthConditions || []),
          userProfile.weightGoal ? `${userProfile.weightGoal} weight` : ''
        ].filter(Boolean)
      };

      console.log('🎯 API Options for meal plan generation:', JSON.stringify(apiOptions, null, 2));

      const mealPlanResult = await ApiService.generateWeeklyMealPlan(apiOptions);

      if (mealPlanResult && mealPlanResult.week) {
        // Enhance next week's plan with complete data
        const enhancedNextWeek = await this.enhanceMealPlanWithData(mealPlanResult.week);

        const nextWeekPlan: WeekPlan = {
          week: enhancedNextWeek,
          weekNumber: nextWeekInfo.weekNumber,
          year: nextWeekInfo.year,
          startDate: nextWeekInfo.startDate.toISOString(),
          endDate: nextWeekInfo.endDate.toISOString(),
          isActive: false, // Not active until next week starts
          generatedAt: Date.now()
        };

        // Store the next week's plan using proper storage method
        await this.storeWeeklyPlan(nextWeekPlan);

        console.log(`✅ Pre-generated plan for Week ${nextWeekInfo.weekNumber}, ${nextWeekInfo.year}`);
      }

    } catch (error) {
      console.error('❌ Error generating next week plan:', error);
    }
  }

  // Clear all cached plans and force regeneration with fresh AI content
  async clearAllPlans(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.WEEKLY_PLANS_KEY);
      await AsyncStorage.removeItem(this.CURRENT_WEEK_KEY);

      // Also clear recipe cache to ensure fresh AI generation
      await RecipeCacheService.clearAllCache();

      console.log('🧹 All weekly plans and recipe cache cleared - fresh AI content will be generated');
    } catch (error) {
      console.error('❌ Error clearing weekly plans:', error);
    }
  }

  // Force regenerate current week with fresh AI content
  async forceRegenerateCurrentWeek(userProfile: any): Promise<WeekPlan | null> {
    try {
      console.log('🔄 Force regenerating current week with fresh AI content...');

      // Clear current week data
      const currentWeekInfo = this.getCurrentWeekInfo();
      await this.deactivateWeek(currentWeekInfo.weekNumber, currentWeekInfo.year);

      // Clear related recipe cache
      await RecipeCacheService.clearAllCache();

      // Generate fresh plan
      return await this.generateWeeklyPlan(userProfile);
    } catch (error) {
      console.error('❌ Error force regenerating current week:', error);
      return null;
    }
  }

  // Validate and fix nutrition data for weekly plan meals
  private async validateAndFixNutritionData(weekData: Array<{day: string; meals: {[key: string]: MealData}}>): Promise<void> {
    try {
      console.log('🔍 Validating nutrition data for weekly plan meals...');

      for (const day of weekData) {
        for (const [mealType, mealData] of Object.entries(day.meals)) {
          // Check if nutrition data looks suspicious (e.g., all meals have same calories)
          const needsRegeneration = this.shouldRegenerateNutrition(mealData, mealType);

          if (needsRegeneration) {
            try {
              console.log(`🔄 Regenerating nutrition for suspicious data: ${mealData.name} (${mealType})`);

              // Force regenerate recipe data
              const newRecipe = await RecipeCacheService.forceRegenerateRecipe(mealData.name, 'weekly_plan');

              // Update meal data with new nutrition
              day.meals[mealType] = {
                ...mealData,
                calories: newRecipe.nutrition.calories,
                protein: newRecipe.nutrition.protein,
                carbs: newRecipe.nutrition.carbs,
                fat: newRecipe.nutrition.fat
              };

              console.log(`✅ Fixed nutrition for ${mealData.name}:`, {
                calories: newRecipe.nutrition.calories,
                protein: newRecipe.nutrition.protein
              });
            } catch (error) {
              console.error(`❌ Error fixing nutrition for ${mealData.name}:`, error);
            }
          }
        }
      }

      console.log('✅ Nutrition data validation completed');

    } catch (error) {
      console.error('❌ Error validating nutrition data:', error);
    }
  }

  // Check if nutrition data should be regenerated
  private shouldRegenerateNutrition(mealData: MealData, mealType: string): boolean {
    // Check for suspicious patterns that indicate static/fallback data
    const calories = mealData.calories;
    const protein = mealData.protein;

    // Flag meals with exactly 350 calories (old static value)
    if (calories === 350) {
      console.log(`⚠️ Found suspicious 350 calorie meal: ${mealData.name}`);
      return true;
    }

    // Flag meals with very low protein (less than 10g for main meals)
    if (['breakfast', 'lunch', 'dinner'].includes(mealType.toLowerCase()) && protein < 10) {
      console.log(`⚠️ Found low protein meal: ${mealData.name} (${protein}g)`);
      return true;
    }

    // Flag meals with unrealistic protein ratios
    const proteinCalories = protein * 4;
    const proteinPercentage = (proteinCalories / calories) * 100;
    if (proteinPercentage < 10 || proteinPercentage > 50) {
      console.log(`⚠️ Found unrealistic protein ratio: ${mealData.name} (${proteinPercentage.toFixed(1)}%)`);
      return true;
    }

    return false;
  }

  // Force regenerate nutrition data for existing weekly plan meals
  async forceRegenerateNutritionData(): Promise<void> {
    try {
      console.log('🔄 Force regenerating nutrition data for all weekly plan meals...');

      const currentPlan = await this.getActiveWeeklyPlan();
      if (!currentPlan) {
        console.log('⚠️ No current week plan found to regenerate nutrition data');
        return;
      }

      // Regenerate nutrition data for each meal
      for (const day of currentPlan.week) {
        for (const [mealType, mealData] of Object.entries(day.meals)) {
          try {
            console.log(`🔄 Regenerating nutrition for: ${mealData.name} (${mealType})`);

            // Force regenerate recipe data
            const newRecipe = await RecipeCacheService.forceRegenerateRecipe(mealData.name, 'weekly_plan');

            // Update meal data with new nutrition
            day.meals[mealType] = {
              ...mealData,
              calories: newRecipe.nutrition.calories,
              protein: newRecipe.nutrition.protein,
              carbs: newRecipe.nutrition.carbs,
              fat: newRecipe.nutrition.fat
            };

            console.log(`✅ Updated nutrition for ${mealData.name}:`, {
              calories: newRecipe.nutrition.calories,
              protein: newRecipe.nutrition.protein
            });
          } catch (error) {
            console.error(`❌ Error regenerating nutrition for ${mealData.name}:`, error);
          }
        }
      }

      // Save updated plan
      await this.saveWeekPlan(currentPlan);
      console.log('✅ All nutrition data regenerated and saved');

    } catch (error) {
      console.error('❌ Error force regenerating nutrition data:', error);
    }
  }
}

export default WeeklyPlanManager.getInstance();
