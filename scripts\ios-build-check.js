
#!/usr/bin/env node

/**
 * iOS Build Compatibility Check Script
 * Validates iOS-specific configurations before EAS build
 */

const fs = require('fs');
const path = require('path');

console.log('🍎 iOS Build Compatibility Check\n');

// Check app.json iOS configuration
function checkAppConfig() {
  console.log('📱 Checking app.json iOS configuration...');
  
  try {
    const appConfig = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    const ios = appConfig.expo.ios;
    
    if (!ios) {
      console.error('❌ No iOS configuration found in app.json');
      return false;
    }
    
    // Check required iOS fields
    const required = ['bundleIdentifier', 'buildNumber'];
    const missing = required.filter(field => !ios[field]);
    
    if (missing.length > 0) {
      console.error(`❌ Missing iOS fields: ${missing.join(', ')}`);
      return false;
    }
    
    // Check Info.plist permissions
    const infoPlist = ios.infoPlist;
    const requiredPermissions = [
      'NSCameraUsageDescription',
      'NSPhotoLibraryUsageDescription',
      'NSMicrophoneUsageDescription',
      'NSLocationWhenInUseUsageDescription',
      'NSMotionUsageDescription'
    ];
    
    const missingPermissions = requiredPermissions.filter(perm => !infoPlist[perm]);
    if (missingPermissions.length > 0) {
      console.warn(`⚠️  Missing iOS permissions: ${missingPermissions.join(', ')}`);
    }
    
    console.log('✅ iOS app.json configuration looks good');
    return true;
  } catch (error) {
    console.error('❌ Error reading app.json:', error.message);
    return false;
  }
}

// Check EAS configuration
function checkEASConfig() {
  console.log('🔧 Checking eas.json configuration...');
  
  try {
    const easConfig = JSON.parse(fs.readFileSync('eas.json', 'utf8'));
    
    if (!easConfig.build.preview.ios) {
      console.error('❌ No iOS preview build configuration found');
      return false;
    }
    
    if (!easConfig.build.production.ios) {
      console.error('❌ No iOS production build configuration found');
      return false;
    }
    
    console.log('✅ EAS configuration looks good');
    return true;
  } catch (error) {
    console.error('❌ Error reading eas.json:', error.message);
    return false;
  }
}

// Check package.json dependencies
function checkDependencies() {
  console.log('📦 Checking iOS-compatible dependencies...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    // Check for iOS-incompatible packages
    const incompatible = [];
    const warnings = [];
    
    // Check for common iOS issues
    if (deps['react-native-permissions']) {
      warnings.push('react-native-permissions may require additional iOS setup');
    }
    
    if (deps['react-native-health']) {
      console.log('📊 Found react-native-health - ensure HealthKit is properly configured');
    }
    
    if (incompatible.length > 0) {
      console.error(`❌ iOS-incompatible packages found: ${incompatible.join(', ')}`);
      return false;
    }
    
    if (warnings.length > 0) {
      warnings.forEach(warning => console.warn(`⚠️  ${warning}`));
    }
    
    console.log('✅ Dependencies look iOS-compatible');
    return true;
  } catch (error) {
    console.error('❌ Error reading package.json:', error.message);
    return false;
  }
}

// Main validation
function main() {
  const checks = [
    checkAppConfig,
    checkEASConfig,
    checkDependencies
  ];
  
  const results = checks.map(check => check());
  const allPassed = results.every(result => result);
  
  console.log('\n📋 iOS Build Check Summary:');
  if (allPassed) {
    console.log('✅ All checks passed! Ready for iOS build.');
    console.log('\n🚀 To build for iOS preview:');
    console.log('   npm run build:ios:preview');
    console.log('\n🚀 To build for iOS production:');
    console.log('   npm run build:ios');
    process.exit(0);
  } else {
    console.log('❌ Some checks failed. Please fix the issues above before building.');
    process.exit(1);
  }
}

main();
