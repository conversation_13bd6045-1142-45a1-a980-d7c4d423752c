import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import Svg, { Circle } from 'react-native-svg';
import { Colors } from '../constants/Colors';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

interface CircularProgressProps {
  size?: number;
  strokeWidth?: number;
  progress: number; // 0-100
  color?: string;
  backgroundColor?: string;
  children?: React.ReactNode;
  showPercentage?: boolean;
  animationDuration?: number;
  style?: any;
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  size = 80,
  strokeWidth = 6,
  progress,
  color = Colors.brand,
  backgroundColor = Colors.borderLight,
  children,
  showPercentage = false,
  animationDuration = 1000,
  style,
}) => {
  const animatedProgress = useSharedValue(0);
  
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  
  useEffect(() => {
    animatedProgress.value = withTiming(progress, {
      duration: animationDuration,
    });
  }, [progress, animationDuration]);

  const animatedStyle = useAnimatedStyle(() => {
    const strokeDashoffset = interpolate(
      animatedProgress.value,
      [0, 100],
      [circumference, 0],
      Extrapolate.CLAMP
    );

    return {
      strokeDashoffset,
    } as any;
  });

  const textAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(animatedProgress.value, [0, 20], [0, 1], Extrapolate.CLAMP),
  }));

  return (
    <View style={[styles.container, { width: size, height: size }, style]}>
      <Svg width={size} height={size} style={styles.svg}>
        {/* Background Circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        
        {/* Progress Circle */}
        <AnimatedCircle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          strokeLinecap="round"
          animatedProps={animatedStyle as any}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </Svg>
      
      {/* Content */}
      <View style={styles.content}>
        {children}
        {showPercentage && (
          <Animated.Text style={[styles.percentage, { color }, textAnimatedStyle]}>
            {Math.round(progress)}%
          </Animated.Text>
        )}
      </View>
    </View>
  );
};

// Premium Nutrition Progress Card
interface NutritionProgressProps {
  label: string;
  value: number;
  target: number;
  unit: string;
  color: string;
  backgroundColor?: string;
  icon?: React.ReactNode;
  size?: number;
}

export const NutritionProgress: React.FC<NutritionProgressProps> = ({
  label,
  value,
  target,
  unit,
  color,
  backgroundColor = Colors.glassLight,
  icon,
  size = 70,
}) => {
  const progress = target > 0 ? Math.min((value / target) * 100, 100) : 0;

  return (
    <View style={[styles.nutritionCard, { backgroundColor }]}>
      <CircularProgress
        size={size}
        progress={progress}
        color={color}
        backgroundColor={Colors.borderLight}
        strokeWidth={4}
        animationDuration={1200}
      >
        {icon && <View style={styles.iconContainer}>{icon}</View>}
      </CircularProgress>
      
      <View style={styles.nutritionInfo}>
        <Text style={[styles.nutritionValue, { color }]}>
          {value}
        </Text>
        <Text style={styles.nutritionTarget}>
          / {target} {unit}
        </Text>
        <Text style={styles.nutritionLabel}>{label}</Text>
      </View>
    </View>
  );
};

// Animated Water Progress
interface WaterProgressProps {
  current: number;
  target: number;
  onPress?: () => void;
}

export const WaterProgress: React.FC<WaterProgressProps> = ({
  current,
  target,
  onPress,
}) => {
  const progress = target > 0 ? (current / target) * 100 : 0;
  const waveHeight = useSharedValue(0);

  useEffect(() => {
    waveHeight.value = withTiming(progress, { duration: 1500 });
  }, [progress]);

  const waveStyle = useAnimatedStyle(() => ({
    height: `${interpolate(waveHeight.value, [0, 100], [0, 80], Extrapolate.CLAMP)}%`,
  }));

  return (
    <View style={styles.waterContainer}>
      <View style={styles.waterGlass}>
        <Animated.View style={[styles.waterFill, waveStyle]} />
        <View style={styles.waterDots}>
          {[...Array(target)].map((_, i) => (
            <View
              key={i}
              style={[
                styles.waterDot,
                {
                  backgroundColor: i < current ? Colors.brand : Colors.borderLight,
                },
              ]}
            />
          ))}
        </View>
      </View>
      <Text style={styles.waterText}>
        {current}/{target} glasses
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  svg: {
    position: 'absolute',
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  percentage: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  nutritionCard: {
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    minWidth: 120,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  iconContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nutritionInfo: {
    marginTop: 12,
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
  },
  nutritionTarget: {
    fontSize: 12,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginTop: 2,
  },
  nutritionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.foreground,
    textAlign: 'center',
    marginTop: 4,
  },
  waterContainer: {
    alignItems: 'center',
    padding: 16,
  },
  waterGlass: {
    width: 60,
    height: 80,
    borderWidth: 2,
    borderColor: Colors.brand,
    borderRadius: 8,
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: Colors.backgroundSecondary,
  },
  waterFill: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.brand,
    opacity: 0.3,
  },
  waterDots: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    height: '100%',
  },
  waterDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    margin: 2,
  },
  waterText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.foreground,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default CircularProgress;
