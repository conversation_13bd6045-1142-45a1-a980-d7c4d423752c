import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import LottieView from 'lottie-react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  runOnJS,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import LottieService, { LottieAnimations, FallbackIcons } from '../services/LottieService';
import { Colors } from '../constants/Colors';

interface LottieIconProps {
  name: keyof typeof LottieAnimations;
  size?: number;
  color?: string;
  loop?: boolean;
  autoPlay?: boolean;
  speed?: number;
  onPress?: () => void;
  style?: any;
  enableHaptics?: boolean;
  animateOnPress?: boolean;
}

const LottieIcon: React.FC<LottieIconProps> = ({
  name,
  size = 24,
  color = Colors.brand,
  loop,
  autoPlay = true,
  speed = 1,
  onPress,
  style,
  enableHaptics = true,
  animateOnPress = true,
}) => {
  const lottieRef = useRef<LottieView>(null);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  // Check if Lottie animation exists
  const hasLottieAnimation = LottieService.hasAnimation(name);
  const animation = LottieService.getAnimation(name);
  const fallbackIcon = LottieService.getFallbackIcon(name);

  // Animation styles
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  // Handle press animation
  const handlePress = () => {
    if (animateOnPress) {
      // Premium button press animation
      scale.value = withSequence(
        withTiming(0.95, { duration: 100 }),
        withSpring(1, {
          damping: 15,
          stiffness: 300,
          mass: 0.8,
        })
      );
    }

    // Haptic feedback
    if (enableHaptics) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Trigger Lottie animation
    if (hasLottieAnimation && lottieRef.current) {
      lottieRef.current.play();
    }

    // Call onPress callback
    if (onPress) {
      onPress();
    }
  };

  // Auto-play animation on mount
  useEffect(() => {
    if (hasLottieAnimation && autoPlay && lottieRef.current) {
      lottieRef.current.play();
    }
  }, [hasLottieAnimation, autoPlay]);

  // Render Lottie animation if available
  if (hasLottieAnimation && animation) {
    return (
      <Animated.View style={[animatedStyle, style]}>
        <TouchableOpacity
          style={[styles.container, { width: size, height: size }]}
          onPress={onPress ? handlePress : undefined}
          disabled={!onPress}
          activeOpacity={0.8}
        >
          <LottieView
            ref={lottieRef}
            source={animation.source}
            style={[styles.lottie, { width: size, height: size }]}
            loop={loop !== undefined ? loop : animation.loop}
            autoPlay={autoPlay && animation.autoPlay}
            speed={speed * (animation.speed || 1)}
            colorFilters={animation.colorFilters || LottieService.applyGreenColorFilter(color)}

          />
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Fallback to Ionicons
  return (
    <Animated.View style={[animatedStyle, style]}>
      <Ionicons
        name={fallbackIcon as any}
        size={size}
        color={color}
        onPress={onPress ? handlePress : undefined}
        style={styles.fallbackIcon}
      />
    </Animated.View>
  );
};

// Premium Lottie Button Component
interface LottieButtonProps extends LottieIconProps {
  children?: React.ReactNode;
  backgroundColor?: string;
  borderRadius?: number;
  padding?: number;
  disabled?: boolean;
}

export const LottieButton: React.FC<LottieButtonProps> = ({
  children,
  backgroundColor = Colors.brand,
  borderRadius = 12,
  padding = 12,
  disabled = false,
  ...lottieProps
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(disabled ? 0.5 : 1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePress = () => {
    if (disabled) return;

    // Premium button press animation
    scale.value = withSequence(
      withTiming(0.96, { duration: 100 }),
      withSpring(1, {
        damping: 15,
        stiffness: 300,
        mass: 0.8,
      })
    );

    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Call original onPress
    if (lottieProps.onPress) {
      lottieProps.onPress();
    }
  };

  return (
    <Animated.View
      style={[
        styles.button,
        {
          backgroundColor,
          borderRadius,
          padding,
        },
        animatedStyle,
      ]}
    >
      <View style={styles.buttonContent}>
        <LottieIcon
          {...lottieProps}
          onPress={handlePress}
          enableHaptics={false} // Handled by button
        />
        {children}
      </View>
    </Animated.View>
  );
};

// Premium Floating Action Button with Lottie
interface LottieFABProps extends LottieIconProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  backgroundColor?: string;
  size?: number;
}

export const LottieFAB: React.FC<LottieFABProps> = ({
  position = 'bottom-right',
  backgroundColor = Colors.brand,
  size = 56,
  ...lottieProps
}) => {
  const scale = useSharedValue(1);
  const translateY = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value },
    ],
  }));

  const handlePress = () => {
    // Premium FAB press animation
    scale.value = withSequence(
      withTiming(0.9, { duration: 100 }),
      withSpring(1.1, { damping: 10, stiffness: 300 }),
      withSpring(1, { damping: 15, stiffness: 200 })
    );

    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    if (lottieProps.onPress) {
      lottieProps.onPress();
    }
  };

  const positionStyle = {
    'bottom-right': { bottom: 20, right: 20 },
    'bottom-left': { bottom: 20, left: 20 },
    'top-right': { top: 60, right: 20 },
    'top-left': { top: 60, left: 20 },
  };

  return (
    <Animated.View
      style={[
        styles.fab,
        {
          backgroundColor,
          width: size,
          height: size,
          borderRadius: size / 2,
        },
        positionStyle[position],
        animatedStyle,
      ]}
    >
      <LottieIcon
        {...lottieProps}
        size={size * 0.5}
        onPress={handlePress}
        enableHaptics={false} // Handled by FAB
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  lottie: {
    // Lottie styles
  },
  fallbackIcon: {
    // Fallback icon styles
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.brandShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  fab: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.brandShadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
});

export default LottieIcon;
