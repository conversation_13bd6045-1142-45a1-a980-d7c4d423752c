import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Colors } from '../constants/Colors';
import VoiceService from '../services/VoiceService';

interface VoiceDebugPanelProps {
  visible: boolean;
  onClose: () => void;
}

const VoiceDebugPanel: React.FC<VoiceDebugPanelProps> = ({ visible, onClose }) => {
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [isListening, setIsListening] = useState(false);

  useEffect(() => {
    if (visible) {
      runDiagnostics();
    }
  }, [visible]);

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  const runDiagnostics = async () => {
    setDebugInfo([]);
    addDebugInfo('🔍 Starting voice recognition diagnostics...');

    // Check platform
    addDebugInfo(`📱 Platform: ${require('react-native').Platform.OS}`);

    // Check development client
    try {
      const isDev = (global as any).__DEV__;
      const nodeEnv = process.env.NODE_ENV;
      const easProfile = process.env.EAS_BUILD_PROFILE;
      
      addDebugInfo(`🔧 __DEV__: ${isDev}`);
      addDebugInfo(`🔧 NODE_ENV: ${nodeEnv}`);
      addDebugInfo(`🔧 EAS_BUILD_PROFILE: ${easProfile}`);
    } catch (error) {
      addDebugInfo(`❌ Error checking environment: ${error}`);
    }

    // Check voice library with FIXED import
    try {
      addDebugInfo(`🔄 Testing CORRECTED voice library import...`);

      // Test the corrected import (without .default)
      const Voice = require('@react-native-voice/voice');
      addDebugInfo(`✅ Voice library imported successfully (corrected method)`);
      addDebugInfo(`🔍 Voice object type: ${typeof Voice}`);
      addDebugInfo(`🔍 Voice is null/undefined: ${Voice === null || Voice === undefined}`);

      if (Voice) {
        const allKeys = Object.keys(Voice);
        const methods = allKeys.filter(key => typeof Voice[key] === 'function');
        const properties = allKeys.filter(key => typeof Voice[key] !== 'function');

        addDebugInfo(`🔍 Total properties: ${allKeys.length}`);
        addDebugInfo(`🔍 Function methods: ${methods.length} - ${methods.slice(0, 5).join(', ')}${methods.length > 5 ? '...' : ''}`);
        addDebugInfo(`🔍 Other properties: ${properties.length} - ${properties.slice(0, 3).join(', ')}${properties.length > 3 ? '...' : ''}`);

        // Check if isAvailable exists and works
        if (typeof Voice.isAvailable === 'function') {
          try {
            const isAvailable = await Voice.isAvailable();
            addDebugInfo(`🔍 Voice.isAvailable(): ${isAvailable}`);
          } catch (error) {
            addDebugInfo(`❌ Voice.isAvailable() error: ${error}`);
          }
        } else {
          addDebugInfo(`❌ Voice.isAvailable method not found`);

          // Try checking if it's in a default property
          if (Voice.default && typeof Voice.default.isAvailable === 'function') {
            addDebugInfo(`🔍 Found Voice.default.isAvailable - trying that...`);
            try {
              const isAvailable = await Voice.default.isAvailable();
              addDebugInfo(`🔍 Voice.default.isAvailable(): ${isAvailable}`);
            } catch (error) {
              addDebugInfo(`❌ Voice.default.isAvailable() error: ${error}`);
            }
          }
        }
      } else {
        addDebugInfo(`❌ Voice object is null/undefined after corrected import`);
      }
    } catch (error) {
      addDebugInfo(`❌ Error importing voice library (corrected method): ${error}`);

      // Try the old method for comparison
      try {
        addDebugInfo(`🔄 Testing OLD import method for comparison...`);
        const VoiceOld = require('@react-native-voice/voice').default;
        addDebugInfo(`🔍 Old method result: ${typeof VoiceOld} (${VoiceOld === null || VoiceOld === undefined ? 'null/undefined' : 'has value'})`);
      } catch (oldError) {
        addDebugInfo(`❌ Old import method also failed: ${oldError}`);
      }
    }

    // Test VoiceService initialization
    try {
      addDebugInfo(`🔄 Testing VoiceService initialization...`);
      const initialized = await VoiceService.initialize();
      addDebugInfo(`🔍 VoiceService.initialize(): ${initialized}`);
    } catch (error) {
      addDebugInfo(`❌ VoiceService initialization error: ${error}`);
    }
  };

  const testVoiceRecognition = async () => {
    if (isListening) {
      await VoiceService.stopListening();
      setIsListening(false);
      addDebugInfo(`🛑 Voice recognition stopped`);
      return;
    }

    addDebugInfo(`🎤 Testing voice recognition...`);
    
    const started = await VoiceService.startListening({
      onStart: () => {
        addDebugInfo(`✅ Voice recognition started successfully`);
        setIsListening(true);
      },
      onResult: (result) => {
        addDebugInfo(`🎯 Voice result: "${result.text}" (confidence: ${result.confidence})`);
        if (result.isFinal) {
          setIsListening(false);
        }
      },
      onPartialResult: (text) => {
        addDebugInfo(`🔄 Partial result: "${text}"`);
      },
      onError: (error) => {
        addDebugInfo(`❌ Voice error: ${error}`);
        setIsListening(false);
      },
      onEnd: () => {
        addDebugInfo(`🏁 Voice recognition ended`);
        setIsListening(false);
      }
    });

    if (!started) {
      addDebugInfo(`❌ Failed to start voice recognition`);
    }
  };

  if (!visible) return null;

  return (
    <View style={styles.overlay}>
      <View style={styles.panel}>
        <View style={styles.header}>
          <Text style={styles.title}>Voice Recognition Debug</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.debugOutput}>
          {debugInfo.map((info, index) => (
            <Text key={index} style={styles.debugText}>
              {info}
            </Text>
          ))}
        </ScrollView>

        <View style={styles.actions}>
          <TouchableOpacity onPress={runDiagnostics} style={styles.button}>
            <Text style={styles.buttonText}>Run Diagnostics</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            onPress={testVoiceRecognition} 
            style={[styles.button, isListening && styles.buttonActive]}
          >
            <Text style={styles.buttonText}>
              {isListening ? 'Stop Voice Test' : 'Test Voice Recognition'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  panel: {
    backgroundColor: '#fcf4ec',
    borderRadius: 16,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.foreground,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.brand,
  },
  debugOutput: {
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 8,
    padding: 12,
    maxHeight: 300,
    marginBottom: 16,
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: Colors.foreground,
    marginBottom: 4,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    backgroundColor: Colors.brand,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  buttonActive: {
    backgroundColor: Colors.destructive,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fcf4ec',
  },
});

export default VoiceDebugPanel;
