#!/bin/bash

# Complete Account Migration Script
# Migrates from Saim7777 to Saim8888 account

echo "🔄 Starting complete account migration from Saim7777 to Saim8888..."
echo ""

# Step 1: Verify login
echo "1️⃣ Checking Expo login status..."
CURRENT_USER=$(npx expo whoami 2>/dev/null || echo "not-logged-in")

if [ "$CURRENT_USER" != "saim8888" ]; then
    echo "❌ Please login to the correct account first:"
    echo "   npx expo logout"
    echo "   npx expo login"
    echo "   Then run this script again"
    exit 1
fi

echo "✅ Logged in as: $CURRENT_USER"
echo ""

# Step 2: Clean all caches
echo "2️⃣ Cleaning all caches and temporary files..."
rm -rf .expo 2>/dev/null || true
rm -rf node_modules/.cache 2>/dev/null || true
npx expo start --clear >/dev/null 2>&1 || true
echo "✅ Caches cleaned"
echo ""

# Step 3: Remove old EAS configuration
echo "3️⃣ Removing old EAS configuration..."
rm -rf .eas 2>/dev/null || true
echo "✅ Old EAS config removed"
echo ""

# Step 4: Initialize new EAS project
echo "4️⃣ Initializing new EAS project..."
npx eas project:init --non-interactive 2>/dev/null || echo "⚠️ Project might already exist"
echo ""

# Step 5: Get new project ID and update app.json
echo "5️⃣ Getting new project information..."
PROJECT_INFO=$(npx eas project:info 2>/dev/null || echo "")

if [[ $PROJECT_INFO == *"Project ID"* ]]; then
    PROJECT_ID=$(echo "$PROJECT_INFO" | grep "Project ID" | sed 's/.*Project ID: //' | tr -d ' ')
    echo "📝 New Project ID: $PROJECT_ID"
    
    # Update app.json with new project ID using Node.js
    node -e "
        const fs = require('fs');
        const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
        appJson.expo.extra.eas.projectId = '$PROJECT_ID';
        fs.writeFileSync('app.json', JSON.stringify(appJson, null, 2));
        console.log('✅ Updated app.json with new project ID');
    "
else
    echo "⚠️ Could not retrieve project ID automatically"
fi
echo ""

# Step 6: Verify configuration
echo "6️⃣ Verifying configuration..."
echo "Current Expo user: $(npx expo whoami)"
echo "Bundle ID: com.saim8888.nutriai"
echo "Package: com.saim8888.nutriai"
echo ""

# Step 7: Build instructions
echo "🎉 Migration complete! Next steps:"
echo ""
echo "1. Start a preview build:"
echo "   npx eas build --profile preview --platform android"
echo ""
echo "2. If build fails, try with clean cache:"
echo "   npx eas build --profile preview --platform android --clear-cache"
echo ""
echo "3. Monitor build progress:"
echo "   npx eas build:list"
echo ""
echo "📱 Your app is now configured for account: saim8888"
echo "🔗 EAS Project: https://expo.dev/accounts/saim8888/projects/nutri-ai-mobile"
