import React, { useState } from 'react';
import { View, Text, StyleSheet, Switch, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Animated, { SlideInLeft } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import { TimeSelectionForm } from '../../components/TimeSelectionForm';

import { OnboardingStackParamList } from './OnboardingNavigator';
import { useOnboarding } from '../../contexts/OnboardingContext';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';

type NotificationSettingsScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'NotificationSettings'>;

const NotificationSettingsScreen: React.FC = () => {
  const navigation = useNavigation<NotificationSettingsScreenNavigationProp>();
  const { data, updateData, nextStep, previousStep } = useOnboarding();

  const [settings, setSettings] = useState({
    mealReminders: data.mealReminders,
    waterReminders: data.waterReminders,
    progressUpdates: data.progressUpdates,
  });

  const [mealTiming, setMealTiming] = useState({
    breakfastTime: data.breakfastTime,
    lunchTime: data.lunchTime,
    dinnerTime: data.dinnerTime,
  });

  const updateSetting = (key: keyof typeof settings, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleNext = () => {
    updateData('mealReminders', settings.mealReminders);
    updateData('waterReminders', settings.waterReminders);
    updateData('progressUpdates', settings.progressUpdates);
    updateData('breakfastTime', mealTiming.breakfastTime);
    updateData('lunchTime', mealTiming.lunchTime);
    updateData('dinnerTime', mealTiming.dinnerTime);

    nextStep();
    navigation.navigate('ProfilePicture');
  };

  const handleBack = () => {
    previousStep();
    navigation.goBack();
  };

  const notificationOptions = [
    {
      key: 'mealReminders' as const,
      title: 'Meal Reminders',
      description: 'Get reminded to log your meals',
      icon: 'restaurant' as const,
      color: '#10B981',
    },
    {
      key: 'waterReminders' as const,
      title: 'Water Reminders',
      description: 'Stay hydrated with regular reminders',
      icon: 'water' as const,
      color: '#3B82F6',
    },
    {
      key: 'progressUpdates' as const,
      title: 'Progress Updates',
      description: 'Weekly summaries of your progress',
      icon: 'trending-up' as const,
      color: '#8B5CF6',
    },
  ];

  const updateMealTime = (mealType: 'breakfast' | 'lunch' | 'dinner', time: string) => {
    const mealTimeKey = `${mealType}Time` as keyof typeof mealTiming;
    setMealTiming(prev => ({ ...prev, [mealTimeKey]: time }));
  };

  return (
    <OnboardingLayout
      title="Notification & Meal Settings"
      subtitle="Choose your preferences and meal times"
      onNext={handleNext}
      onBack={handleBack}
      showSkip={true}
      onSkip={handleNext}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Notification Settings */}
        {notificationOptions.map((option, index) => (
          <Animated.View
            key={option.key}
            entering={SlideInLeft.delay(200 + index * 100).duration(600)}
            style={styles.settingCard}
          >
            <View style={styles.settingContent}>
              <View style={[styles.iconContainer, { backgroundColor: `${option.color}20` }]}>
                <Ionicons name={option.icon} size={24} color={option.color} />
              </View>

              <View style={styles.textContainer}>
                <Text style={styles.settingTitle}>{option.title}</Text>
                <Text style={styles.settingDescription}>{option.description}</Text>
              </View>

              <Switch
                value={settings[option.key]}
                onValueChange={(value) => updateSetting(option.key, value)}
                trackColor={{ false: '#fcf4ec', true: '#6B7C5A' }}
                thumbColor={settings[option.key] ? '#fcf4ec' : '#fcf4ec'}
                ios_backgroundColor="#fcf4ec"
              />
            </View>
          </Animated.View>
        ))}

        {/* Meal Timing Section */}
        <Animated.View
          entering={SlideInLeft.delay(600).duration(600)}
          style={styles.sectionContainer}
        >
          <TimeSelectionForm
            breakfastTime={mealTiming.breakfastTime}
            lunchTime={mealTiming.lunchTime}
            dinnerTime={mealTiming.dinnerTime}
            onTimeChange={(mealType, time) => updateMealTime(mealType, time)}
          />
        </Animated.View>
      </ScrollView>
    </OnboardingLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 32,
  },
  settingCard: {
    backgroundColor: '#fcf4ec',
    borderRadius: 24,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#fcf4ec',
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#fcf4ec',
  },
  textContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  settingDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    lineHeight: 20,
  },
  sectionContainer: {
    marginTop: 32,
    backgroundColor: '#fcf4ec',
    borderRadius: 24,
    padding: 24,
    borderWidth: 1,
    borderColor: '#fcf4ec',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    marginBottom: 24,
    lineHeight: 20,
  },

});

export default NotificationSettingsScreen;
