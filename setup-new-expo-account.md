# Expo Account Migration: Saim9999 → Saim0000

## ✅ Configuration Files Updated

The following files have been updated with the new account information:

### 📱 **app.json Changes:**
- **iOS Bundle ID**: `com.saim9999.nutriai` → `com.saim0000.nutriai`
- **Android Package**: `com.saim9999.nutriai` → `com.saim0000.nutriai`
- **Owner**: `saim9999` → `saim0000`
- **Project ID**: Marked for regeneration

## 🔧 **Required Steps to Complete Migration**

### 1. **Login to New Expo Account**
```bash
# Logout from current account
npx expo logout

# Login to new account
npx expo login
# Enter username: Saim0000
# Enter password: [your password]
```

### 2. **Initialize New EAS Project**
```bash
# Initialize EAS project (this will generate a new project ID)
npx eas init

# This will:
# - Create a new project under Saim0000 account
# - Generate a new project ID
# - Update app.json with the new project ID
```

### 3. **Configure EAS Build**
```bash
# Configure EAS build profiles
npx eas build:configure

# This will ensure eas.json is properly set up for the new account
```

### 4. **Verify Configuration**
```bash
# Check project status
npx eas project:info

# Should show:
# - Owner: saim0000
# - Project name: nutri-ai-mobile
# - New project ID
```

### 5. **Test Build (Optional)**
```bash
# Test development build
npx eas build --platform android --profile development

# Or preview build
npx eas build --platform android --profile preview
```

## 📋 **What Changed**

### **Bundle Identifiers:**
- **iOS**: `com.saim0000.nutriai`
- **Android**: `com.saim0000.nutriai`

### **Account Ownership:**
- **Previous**: saim9999
- **New**: saim0000

### **Project Management:**
- New EAS project will be created under Saim0000 account
- All future builds will be associated with the new account
- Previous builds under Saim9999 will remain separate

## ⚠️ **Important Notes**

1. **Clean Builds**: You may need to run clean builds after the migration
2. **App Store**: If you plan to publish, ensure the bundle IDs don't conflict with existing apps
3. **Team Access**: Make sure the new account has proper permissions
4. **Credentials**: EAS will manage new signing credentials for the new account

## 🚀 **Next Steps After Migration**

1. Run the commands above in order
2. Test a development build to ensure everything works
3. Update any CI/CD pipelines with new account credentials
4. Update team members about the account change

## 📱 **Build Commands (After Setup)**

```bash
# Development builds
npm run build:android
npm run build:ios

# Preview builds  
npx eas build --platform android --profile preview
npx eas build --platform ios --profile preview

# Production builds
npx eas build --platform all --profile production
```

---

**Status**: ✅ Configuration files updated, ready for EAS initialization
