import { useEffect, useRef } from 'react';
import {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withDelay,
  runOnJS,
  interpolate,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { AnimationConfig, AnimationPresets, GestureAnimations, HapticAnimations, AnimationSequences } from '../utils/AnimationUtils';

// Enhanced Press Animation Hook
export const usePressAnimation = (
  onPress?: () => void,
  hapticFeedback: 'light' | 'medium' | 'heavy' | 'none' = 'light'
) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePressIn = () => {
    GestureAnimations.pressIn(scale, opacity);
    
    if (hapticFeedback !== 'none') {
      const hapticType = {
        light: Haptics.ImpactFeedbackStyle.Light,
        medium: Haptics.ImpactFeedbackStyle.Medium,
        heavy: Haptics.ImpactFeedbackStyle.Heavy,
      }[hapticFeedback];
      
      Haptics.impactAsync(hapticType);
    }
  };

  const handlePressOut = () => {
    GestureAnimations.pressOut(scale, opacity);
    if (onPress) {
      setTimeout(onPress, 100);
    }
  };

  return {
    animatedStyle,
    handlePressIn,
    handlePressOut,
  };
};

// Bounce Animation Hook
export const useBounceAnimation = (trigger: boolean, intensity = 1.1) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  useEffect(() => {
    if (trigger) {
      AnimationPresets.bounce(scale);
    }
  }, [trigger, scale]);

  return { animatedStyle, scale };
};

// Fade Animation Hook
export const useFadeAnimation = (visible: boolean, delay = 0) => {
  const opacity = useSharedValue(visible ? 1 : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  useEffect(() => {
    if (visible) {
      AnimationPresets.fadeIn(opacity, delay);
    } else {
      AnimationPresets.fadeOut(opacity, delay);
    }
  }, [visible, delay, opacity]);

  return { animatedStyle, opacity };
};

// Slide Animation Hook
export const useSlideAnimation = (
  visible: boolean,
  direction: 'left' | 'right' | 'up' | 'down' = 'up',
  distance = 100,
  delay = 0
) => {
  const translateX = useSharedValue(direction === 'left' ? -distance : direction === 'right' ? distance : 0);
  const translateY = useSharedValue(direction === 'up' ? distance : direction === 'down' ? -distance : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  useEffect(() => {
    if (visible) {
      if (direction === 'left' || direction === 'right') {
        AnimationPresets.slideInFromLeft(translateX, delay);
      } else {
        AnimationPresets.slideInFromTop(translateY, delay);
      }
    } else {
      translateX.value = direction === 'left' ? -distance : direction === 'right' ? distance : 0;
      translateY.value = direction === 'up' ? distance : direction === 'down' ? -distance : 0;
    }
  }, [visible, direction, distance, delay, translateX, translateY]);

  return { animatedStyle, translateX, translateY };
};

// Scale Animation Hook
export const useScaleAnimation = (visible: boolean, delay = 0, fromScale = 0) => {
  const scale = useSharedValue(visible ? 1 : fromScale);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  useEffect(() => {
    if (visible) {
      AnimationPresets.scaleIn(delay);
      scale.value = withDelay(delay, withSpring(1, AnimationConfig.spring.bouncy));
    } else {
      scale.value = withSpring(fromScale, AnimationConfig.spring.snappy);
    }
  }, [visible, delay, fromScale, scale]);

  return { animatedStyle, scale };
};

// Rotation Animation Hook
export const useRotationAnimation = (trigger: boolean, degrees = 360) => {
  const rotation = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  useEffect(() => {
    if (trigger) {
      AnimationPresets.rotate360(rotation);
    }
  }, [trigger, rotation]);

  return { animatedStyle, rotation };
};

// Progress Animation Hook
export const useProgressAnimation = (progress: number, duration = 1000) => {
  const animatedProgress = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    width: `${animatedProgress.value}%`,
  }));

  useEffect(() => {
    AnimationPresets.progressFill(animatedProgress, progress * 100, duration);
  }, [progress, duration, animatedProgress]);

  return { animatedStyle, animatedProgress };
};

// Staggered List Animation Hook
export const useStaggeredListAnimation = (itemCount: number, visible: boolean, staggerDelay = 100) => {
  const animations = useRef(
    Array.from({ length: itemCount }, () => useSharedValue(0))
  ).current;

  useEffect(() => {
    if (visible) {
      AnimationPresets.staggeredListIn(animations, 0, staggerDelay);
    } else {
      animations.forEach((animation) => {
        animation.value = 0;
      });
    }
  }, [visible, animations, staggerDelay]);

  const getItemStyle = (index: number) => {
    return useAnimatedStyle(() => ({
      opacity: animations[index]?.value || 0,
      transform: [
        { scale: animations[index]?.value || 0 },
        { translateY: interpolate(animations[index]?.value || 0, [0, 1], [20, 0]) },
      ],
    }));
  };

  return { getItemStyle, animations };
};

// Shake Animation Hook
export const useShakeAnimation = (trigger: boolean, intensity = 10) => {
  const translateX = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  useEffect(() => {
    if (trigger) {
      AnimationPresets.shake(translateX, intensity);
    }
  }, [trigger, translateX, intensity]);

  return { animatedStyle, translateX };
};

// Pulse Animation Hook
export const usePulseAnimation = (active: boolean, intensity = 1.1) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  useEffect(() => {
    if (active) {
      AnimationPresets.pulse(scale, intensity);
    } else {
      scale.value = withSpring(1, AnimationConfig.spring.gentle);
    }
  }, [active, scale, intensity]);

  return { animatedStyle, scale };
};

// Breathing Animation Hook
export const useBreathingAnimation = (active: boolean, intensity = 0.05) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  useEffect(() => {
    if (active) {
      AnimationPresets.breathe(scale, intensity);
    } else {
      scale.value = withSpring(1, AnimationConfig.spring.gentle);
    }
  }, [active, scale, intensity]);

  return { animatedStyle, scale };
};

// Morphing Animation Hook
export const useMorphAnimation = (visible: boolean, delay = 0) => {
  const scale = useSharedValue(visible ? 1 : 0.8);
  const opacity = useSharedValue(visible ? 1 : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  useEffect(() => {
    if (visible) {
      AnimationPresets.morphIn(scale, opacity, delay);
    } else {
      AnimationPresets.morphOut(scale, opacity, delay);
    }
  }, [visible, delay, scale, opacity]);

  return { animatedStyle, scale, opacity };
};

// Card Flip Animation Hook
export const useCardFlipAnimation = (trigger: boolean, onFlip?: () => void) => {
  const rotateY = useSharedValue(0);

  const frontAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotateY: `${rotateY.value}deg` }],
    opacity: rotateY.value < 90 ? 1 : 0,
  }));

  const backAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotateY: `${rotateY.value + 180}deg` }],
    opacity: rotateY.value >= 90 ? 1 : 0,
  }));

  useEffect(() => {
    if (trigger) {
      AnimationPresets.cardFlip(rotateY, onFlip);
    }
  }, [trigger, rotateY, onFlip]);

  return { frontAnimatedStyle, backAnimatedStyle, rotateY };
};

// Loading Animation Hook
export const useLoadingAnimation = (loading: boolean, elementCount = 3) => {
  const animations = useRef(
    Array.from({ length: elementCount }, () => useSharedValue(1))
  ).current;

  useEffect(() => {
    if (loading) {
      AnimationSequences.loading(animations);
    } else {
      animations.forEach((animation) => {
        animation.value = withSpring(1, AnimationConfig.spring.gentle);
      });
    }
  }, [loading, animations]);

  const getElementStyle = (index: number) => {
    return useAnimatedStyle(() => ({
      opacity: animations[index]?.value || 1,
    }));
  };

  return { getElementStyle, animations };
};

// Success Animation Hook
export const useSuccessAnimation = (trigger: boolean, onComplete?: () => void) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  useEffect(() => {
    if (trigger) {
      HapticAnimations.success(scale, onComplete);
    }
  }, [trigger, scale, onComplete]);

  return { animatedStyle, scale, opacity };
};

// Error Animation Hook
export const useErrorAnimation = (trigger: boolean, onComplete?: () => void) => {
  const translateX = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  useEffect(() => {
    if (trigger) {
      HapticAnimations.error(translateX, onComplete);
    }
  }, [trigger, translateX, onComplete]);

  return { animatedStyle, translateX };
};

export {
  AnimationConfig,
  AnimationPresets,
  GestureAnimations,
  HapticAnimations,
} from '../utils/AnimationUtils';
