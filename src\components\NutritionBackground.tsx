import React from 'react';
import { View, StyleSheet, ImageBackground } from 'react-native';

interface NutritionBackgroundProps {
  children: React.ReactNode;
  variant?: 'onboarding' | 'main';
}

const NutritionBackground: React.FC<NutritionBackgroundProps> = ({
  children,
  variant = 'main',
}) => {
  return (
    <View style={styles.container}>
      {/* Beautiful Background with New Screens Background Image */}
      <ImageBackground
        source={require('../../assets/screens background.jpg')}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        {/* Semi-transparent overlay for content readability while showing background */}
        <View style={[
          styles.overlay,
          variant === 'onboarding' && styles.onboardingOverlay
        ]} />
      </ImageBackground>

      {/* Content */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(252, 244, 236, 0.85)', // 85% opacity to show background images
  },
  onboardingOverlay: {
    backgroundColor: 'rgba(252, 244, 236, 0.85)', // 85% opacity to show background images
  },
  content: {
    flex: 1,
    zIndex: 10,
  },
});

export default NutritionBackground;
