import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface TimeSelectionFormProps {
  breakfastTime: string;
  lunchTime: string;
  dinnerTime: string;
  onTimeChange: (mealType: 'breakfast' | 'lunch' | 'dinner', time: string) => void;
}

export const TimeSelectionForm: React.FC<TimeSelectionFormProps> = ({
  breakfastTime,
  lunchTime,
  dinnerTime,
  onTimeChange,
}) => {
  const [expandedMeal, setExpandedMeal] = useState<string | null>(null);

  // Predefined time options for each meal
  const breakfastTimes = [
    '06:00', '06:30', '07:00', '07:30', '08:00', '08:30', '09:00', '09:30', '10:00'
  ];
  
  const lunchTimes = [
    '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00'
  ];
  
  const dinnerTimes = [
    '17:00', '17:30', '18:00', '18:30', '19:00', '19:30', '20:00', '20:30', '21:00'
  ];

  const formatDisplayTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const getMealTimes = (mealType: string) => {
    switch (mealType) {
      case 'breakfast': return breakfastTimes;
      case 'lunch': return lunchTimes;
      case 'dinner': return dinnerTimes;
      default: return [];
    }
  };

  const getCurrentTime = (mealType: string) => {
    switch (mealType) {
      case 'breakfast': return breakfastTime;
      case 'lunch': return lunchTime;
      case 'dinner': return dinnerTime;
      default: return '';
    }
  };

  const getMealIcon = (mealType: string) => {
    switch (mealType) {
      case 'breakfast': return 'sunny-outline';
      case 'lunch': return 'partly-sunny-outline';
      case 'dinner': return 'moon-outline';
      default: return 'time-outline';
    }
  };

  const getMealColor = (mealType: string) => {
    switch (mealType) {
      case 'breakfast': return '#FF9500';
      case 'lunch': return '#34C759';
      case 'dinner': return '#5856D6';
      default: return '#6B7C5A';
    }
  };

  const handleTimeSelect = (mealType: 'breakfast' | 'lunch' | 'dinner', time: string) => {
    onTimeChange(mealType, time);
    setExpandedMeal(null);
  };

  const renderMealTimeSelector = (mealType: 'breakfast' | 'lunch' | 'dinner', label: string) => {
    const isExpanded = expandedMeal === mealType;
    const currentTime = getCurrentTime(mealType);
    const timeOptions = getMealTimes(mealType);
    const icon = getMealIcon(mealType);
    const color = getMealColor(mealType);

    return (
      <View key={mealType} style={styles.mealContainer}>
        <TouchableOpacity
          style={[styles.mealHeader, { borderColor: color }]}
          onPress={() => setExpandedMeal(isExpanded ? null : mealType)}
        >
          <View style={styles.mealHeaderLeft}>
            <View style={[styles.iconContainer, { backgroundColor: color }]}>
              <Ionicons name={icon as any} size={20} color="#fcf4ec" />
            </View>
            <View>
              <Text style={styles.mealLabel}>{label}</Text>
              <Text style={styles.selectedTime}>
                {currentTime ? formatDisplayTime(currentTime) : 'Select time'}
              </Text>
            </View>
          </View>
          <Ionicons 
            name={isExpanded ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color="#6B7C5A" 
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.timeOptionsContainer}>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.timeOptionsScroll}
            >
              {timeOptions.map((time) => (
                <TouchableOpacity
                  key={time}
                  style={[
                    styles.timeOption,
                    currentTime === time && [styles.selectedTimeOption, { backgroundColor: color }]
                  ]}
                  onPress={() => handleTimeSelect(mealType, time)}
                >
                  <Text style={[
                    styles.timeOptionText,
                    currentTime === time && styles.selectedTimeOptionText
                  ]}>
                    {formatDisplayTime(time)}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Meal Times</Text>
      <Text style={styles.subtitle}>When do you usually eat your meals?</Text>
      
      {renderMealTimeSelector('breakfast', 'Breakfast')}
      {renderMealTimeSelector('lunch', 'Lunch')}
      {renderMealTimeSelector('dinner', 'Dinner')}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
  },
  mealContainer: {
    marginBottom: 16,
  },
  mealHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fcf4ec',
    padding: 16,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E5E5E5',
  },
  mealHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  mealLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  selectedTime: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  timeOptionsContainer: {
    backgroundColor: '#fcf4ec',
    marginTop: 8,
    borderRadius: 12,
    padding: 12,
  },
  timeOptionsScroll: {
    paddingHorizontal: 4,
  },
  timeOption: {
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  selectedTimeOption: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  timeOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7C5A',
  },
  selectedTimeOptionText: {
    color: '#fcf4ec',
    fontWeight: '600',
  },
});
