import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedProps,
  withTiming,
  withSpring,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import Svg, { Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { Colors } from '../constants/Colors';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

interface WeightProgressCircleProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  currentWeight: number;
  targetWeight: number;
  weightLost: number;
  isOnTrack: boolean;
  style?: any;
}

const WeightProgressCircle: React.FC<WeightProgressCircleProps> = ({
  progress,
  size = 200,
  strokeWidth = 12,
  currentWeight,
  targetWeight,
  weightLost,
  isOnTrack,
  style,
}) => {
  const animatedProgress = useSharedValue(0);
  const scaleValue = useSharedValue(0.8);

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;

  useEffect(() => {
    // Animate progress with spring
    animatedProgress.value = withTiming(progress, {
      duration: 1500,
      easing: Easing.out(Easing.cubic),
    });

    // Scale animation for entrance
    scaleValue.value = withSpring(1, {
      damping: 15,
      stiffness: 150,
    });
  }, [progress]);

  const animatedProps = useAnimatedProps(() => {
    const strokeDashoffset = interpolate(
      animatedProgress.value,
      [0, 100],
      [circumference, 0]
    );

    return {
      strokeDashoffset,
      transform: [{ scale: scaleValue.value }],
    };
  });

  const getProgressColor = () => {
    if (progress >= 75) return Colors.brand; // Dark green for excellent progress
    if (progress >= 50) return Colors.brand; // Dark green for good progress
    if (progress >= 25) return Colors.brandSecondary; // Olive green for moderate progress
    return Colors.brandTertiary; // Light green for low progress - NO RED
  };

  const getMotivationalMessage = () => {
    if (progress >= 90) return 'Almost there! 🎉';
    if (progress >= 75) return 'Excellent progress! 💪';
    if (progress >= 50) return 'Great job! Keep going! 🔥';
    if (progress >= 25) return 'Good start! Stay focused! 💯';
    return 'Your journey begins! 🌟';
  };

  const getStatusIcon = () => {
    if (progress >= 75) return '🏆';
    if (progress >= 50) return '⭐';
    if (progress >= 25) return '📈';
    return '🎯';
  };

  return (
    <View style={[styles.container, style]}>
      <Animated.View style={[styles.circleContainer, { transform: [{ scale: scaleValue }] }]}>
        <Svg width={size} height={size} style={styles.svg}>
          <Defs>
            <LinearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor={getProgressColor()} stopOpacity="1" />
              <Stop offset="100%" stopColor={getProgressColor()} stopOpacity="0.7" />
            </LinearGradient>
          </Defs>
          
          {/* Background Circle with stronger outline */}
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={Colors.brandMuted}
            strokeWidth={strokeWidth}
            fill="rgba(255, 255, 255, 0.8)" // Add #fcf4ec background for visibility
          />

          {/* Outer border circle for better definition */}
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius + strokeWidth / 2}
            stroke="rgba(107, 124, 90, 0.2)"
            strokeWidth={1}
            fill="none"
          />

          {/* Progress Circle with enhanced visibility */}
          <AnimatedCircle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="url(#progressGradient)"
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={circumference}
            animatedProps={animatedProps}
            transform={`rotate(-90 ${size / 2} ${size / 2})`}
          />
        </Svg>

        {/* Center Content - COMPLETELY REDESIGNED FOR CLARITY */}
        <View style={styles.centerContent}>
          {/* Main Progress Display - LARGER AND CLEARER */}
          <View style={styles.mainProgressContainer}>
            <Text style={styles.progressText}>{Math.round(progress)}%</Text>
            <Text style={styles.progressLabel}>COMPLETE</Text>
          </View>
        </View>
      </Animated.View>

      {/* Weight Display - MOVED OUTSIDE CIRCLE TO PREVENT OVERLAP */}
      <View style={styles.weightDisplayContainer}>
        <View style={styles.weightItem}>
          <Text style={styles.weightValue}>{currentWeight.toFixed(1)}</Text>
          <Text style={styles.weightLabel}>CURRENT</Text>
        </View>
        <View style={styles.weightSeparator} />
        <View style={styles.weightItem}>
          <Text style={styles.weightValue}>{targetWeight.toFixed(1)}</Text>
          <Text style={styles.weightLabel}>TARGET</Text>
        </View>
      </View>

      {/* Weight Lost Display - SIMPLIFIED AND CLEARER */}
      <View style={styles.weightLostContainer}>
        <Text style={[styles.weightLostValue, { color: getProgressColor() }]}>
          {weightLost.toFixed(1)} kg
        </Text>
        <Text style={styles.weightLostLabel}>LOST</Text>
      </View>

      {/* Status Indicator - CLEANER DESIGN */}
      <View style={[styles.statusIndicator, {
        backgroundColor: isOnTrack ? Colors.brand : Colors.brandSecondary,
        borderColor: isOnTrack ? Colors.brand : Colors.brandSecondary
      }]}>
        <Text style={styles.statusText}>
          {isOnTrack ? '✓ On Track' : 'Needs Focus'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6, // Minimal gap for ultra compactness
    backgroundColor: 'rgba(255, 255, 255, 0.95)', // Strong background for visibility
    borderRadius: 16, // Smaller radius
    padding: 8, // Minimal padding around the entire component
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  circleContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Additional background for circle
    borderRadius: 100, // Make it circular
    padding: 4, // Minimal padding around the circle
  },
  svg: {
    position: 'absolute',
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    width: '100%', // Ensure proper width
  },

  // COMPLETELY REDESIGNED CENTER CONTENT STYLES
  mainProgressContainer: {
    alignItems: 'center',
    marginBottom: 8, // Reduced margin for compactness
  },
  progressText: {
    fontSize: 26, // Reduced for smaller circle
    fontWeight: '900',
    color: Colors.brand,
    lineHeight: 30, // Adjusted line height
    letterSpacing: -0.5,
  },
  progressLabel: {
    fontSize: 9, // Reduced font size
    fontWeight: '700',
    color: Colors.mutedForeground,
    marginTop: 0,
    textTransform: 'uppercase',
    letterSpacing: 0.8, // Reduced letter spacing
  },

  // WEIGHT DISPLAY - POSITIONED OUTSIDE CIRCLE TO PREVENT OVERLAP
  weightDisplayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    paddingHorizontal: 16,
    marginTop: 8, // Space from circle
    marginBottom: 4, // Space before weight lost
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    paddingVertical: 6,
  },
  weightItem: {
    alignItems: 'center',
    flex: 1,
  },
  weightValue: {
    fontSize: 14, // Reduced for compactness
    fontWeight: '800',
    color: Colors.foreground,
    lineHeight: 16,
  },
  weightLabel: {
    fontSize: 8, // Reduced font size
    fontWeight: '600',
    color: Colors.mutedForeground,
    marginTop: 1, // Reduced margin
    textTransform: 'uppercase',
    letterSpacing: 0.3, // Reduced letter spacing
  },
  weightSeparator: {
    width: 1,
    height: 20,
    backgroundColor: Colors.brandMuted,
    marginHorizontal: 12,
  },

  // WEIGHT LOST - ULTRA COMPACT DESIGN
  weightLostContainer: {
    alignItems: 'center',
    marginTop: 4, // Minimal margin
    paddingHorizontal: 12, // Further reduced padding
    paddingVertical: 4, // Minimal padding
    backgroundColor: 'rgba(107, 124, 90, 0.08)',
    borderRadius: 12, // Smaller radius
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.15)',
    minWidth: 80, // Further reduced width
  },
  weightLostValue: {
    fontSize: 14, // Further reduced font size
    fontWeight: '700', // Reduced weight
    lineHeight: 16, // Reduced line height
  },
  weightLostLabel: {
    fontSize: 8, // Further reduced font size
    fontWeight: '500', // Reduced weight
    color: Colors.mutedForeground,
    marginTop: 0, // No margin
    textTransform: 'uppercase',
    letterSpacing: 0.3, // Reduced letter spacing
  },

  // ULTRA COMPACT STATUS INDICATOR - NO OVERLAP
  statusIndicator: {
    paddingHorizontal: 12, // Further reduced padding
    paddingVertical: 3, // Minimal padding
    borderRadius: 16, // Smaller radius
    marginTop: 4, // Minimal margin to prevent overlap
    borderWidth: 1,
    shadowColor: Colors.brand,
    shadowOffset: { width: 0, height: 1 }, // Minimal shadow
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    minWidth: 100, // Further reduced width
    alignSelf: 'center', // Center the indicator
  },
  statusText: {
    fontSize: 9, // Further reduced font size
    fontWeight: '600', // Reduced weight
    color: '#fcf4ec',
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.2, // Minimal letter spacing
  },
});

export default WeightProgressCircle;
