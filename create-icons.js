const fs = require('fs');
const { createCanvas } = require('canvas');

function createNutriAIIcon(size, hasBackground = true) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');

    // Background (only for main icon)
    if (hasBackground) {
        ctx.fillStyle = '#fcf4ec';
        ctx.fillRect(0, 0, size, size);
    }

    // Calculate text size based on canvas size
    const fontSize = size * 0.12; // 12% of canvas size
    const leafSize = size * 0.1; // 10% of canvas size

    // Text styling
    ctx.fillStyle = '#6B7C5A';
    ctx.font = `bold ${fontSize}px Inter, -apple-system, BlinkMacSystemFont, sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // Draw "NutriAI" text
    const text = 'NutriAI';
    const textMetrics = ctx.measureText(text);
    const textX = size / 2;
    const textY = size / 2;
    
    ctx.fillText(text, textX, textY);

    // Draw leaf icon after text
    const leafX = textX + (textMetrics.width / 2) + (leafSize * 0.6);
    const leafY = textY - (fontSize * 0.1);

    // Enhanced leaf shape
    ctx.beginPath();
    ctx.moveTo(leafX, leafY + leafSize * 0.3);
    ctx.quadraticCurveTo(leafX + leafSize * 0.6, leafY - leafSize * 0.4, leafX + leafSize * 1.2, leafY);
    ctx.quadraticCurveTo(leafX + leafSize * 1.0, leafY + leafSize * 0.3, leafX + leafSize * 0.8, leafY + leafSize * 0.6);
    ctx.quadraticCurveTo(leafX + leafSize * 0.4, leafY + leafSize * 0.7, leafX, leafY + leafSize * 0.3);
    ctx.fillStyle = '#6B7C5A';
    ctx.fill();

    // Leaf vein
    ctx.beginPath();
    ctx.moveTo(leafX, leafY + leafSize * 0.3);
    ctx.quadraticCurveTo(leafX + leafSize * 0.4, leafY + leafSize * 0.1, leafX + leafSize * 0.8, leafY + leafSize * 0.15);
    ctx.strokeStyle = '#fcf4ec';
    ctx.lineWidth = size * 0.003;
    ctx.stroke();

    return canvas;
}

// Create icons
const mainIcon = createNutriAIIcon(1024, true); // With #fcf4ec background
const adaptiveIcon = createNutriAIIcon(1024, false); // Transparent
const splashIcon = createNutriAIIcon(1024, false); // Transparent

// Save icons
fs.writeFileSync('assets/icon.png', mainIcon.toBuffer('image/png'));
fs.writeFileSync('assets/adaptive-icon.png', adaptiveIcon.toBuffer('image/png'));
fs.writeFileSync('assets/splash-icon.png', splashIcon.toBuffer('image/png'));

console.log('✅ Icons created successfully!');
console.log('📁 Files saved:');
console.log('   - assets/icon.png (1024x1024, #fcf4ec background)');
console.log('   - assets/adaptive-icon.png (1024x1024, transparent)');
console.log('   - assets/splash-icon.png (1024x1024, transparent)');
